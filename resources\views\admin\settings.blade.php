@extends('layouts.admin')

@section('title', 'ตั้งค่าเว็บไซต์ - ระบบจัดการ')

@section('breadcrumb')
<li class="breadcrumb-item active">ตั้งค่าเว็บไซต์</li>
@endsection

@section('content')
<div class="content-safe-area">
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">ตั้งค่าเว็บไซต์</h1>
    <a href="{{ route('home') }}" class="btn btn-outline-primary" target="_blank">
        <i class="fas fa-external-link-alt me-2"></i>ดูเว็บไซต์
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <form action="{{ route('admin.settings.update') }}" method="POST">
            @csrf
            @method('PUT')
            
            <!-- General Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">ข้อมูลทั่วไป</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="site_name" class="form-label">ชื่อเว็บไซต์ <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('site_name') is-invalid @enderror" 
                               id="site_name" name="site_name" value="{{ old('site_name', $settings['site_name']) }}" required>
                        @error('site_name')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="site_description" class="form-label">คำอธิบายเว็บไซต์ <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('site_description') is-invalid @enderror" 
                                  id="site_description" name="site_description" rows="3" required>{{ old('site_description', $settings['site_description']) }}</textarea>
                        @error('site_description')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">คำอธิบายนี้จะแสดงในหน้าหลักและ meta description</div>
                    </div>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">ข้อมูลติดต่อ</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="contact_phone" class="form-label">เบอร์โทรศัพท์ <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('contact_phone') is-invalid @enderror" 
                                       id="contact_phone" name="contact_phone" value="{{ old('contact_phone', $settings['contact_phone']) }}" required>
                                @error('contact_phone')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="contact_email" class="form-label">อีเมล <span class="text-danger">*</span></label>
                                <input type="email" class="form-control @error('contact_email') is-invalid @enderror" 
                                       id="contact_email" name="contact_email" value="{{ old('contact_email', $settings['contact_email']) }}" required>
                                @error('contact_email')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="contact_address" class="form-label">ที่อยู่ <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('contact_address') is-invalid @enderror" 
                                  id="contact_address" name="contact_address" rows="3" required>{{ old('contact_address', $settings['contact_address']) }}</textarea>
                        @error('contact_address')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
            
            <!-- Social Media -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">โซเชียลมีเดีย</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="facebook_url" class="form-label">Facebook URL</label>
                                <input type="url" class="form-control @error('facebook_url') is-invalid @enderror" 
                                       id="facebook_url" name="facebook_url" value="{{ old('facebook_url', $settings['facebook_url']) }}" 
                                       placeholder="https://facebook.com/yourpage">
                                @error('facebook_url')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="line_id" class="form-label">Line ID</label>
                                <input type="text" class="form-control @error('line_id') is-invalid @enderror" 
                                       id="line_id" name="line_id" value="{{ old('line_id', $settings['line_id']) }}" 
                                       placeholder="@yourlineid">
                                @error('line_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">ใส่ Line ID โดยไม่ต้องใส่ @ ข้างหน้า</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>บันทึกการตั้งค่า
                </button>
                <button type="reset" class="btn btn-secondary">รีเซ็ต</button>
            </div>
        </form>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">ตัวอย่างการแสดงผล</h5>
            </div>
            <div class="card-body">
                <div class="preview-section">
                    <h6>ชื่อเว็บไซต์:</h6>
                    <p class="fw-bold" id="preview-site-name">{{ $settings['site_name'] }}</p>
                    
                    <h6>คำอธิบาย:</h6>
                    <p class="text-muted" id="preview-site-description">{{ $settings['site_description'] }}</p>
                    
                    <h6>ข้อมูลติดต่อ:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-phone text-primary me-2"></i><span id="preview-phone">{{ $settings['contact_phone'] }}</span></li>
                        <li><i class="fas fa-envelope text-primary me-2"></i><span id="preview-email">{{ $settings['contact_email'] }}</span></li>
                        <li><i class="fas fa-map-marker-alt text-primary me-2"></i><span id="preview-address">{{ $settings['contact_address'] }}</span></li>
                    </ul>
                    
                    <h6>โซเชียลมีเดีย:</h6>
                    <div class="d-flex gap-2">
                        @if($settings['facebook_url'])
                        <a href="{{ $settings['facebook_url'] }}" class="btn btn-sm btn-outline-primary" target="_blank">
                            <i class="fab fa-facebook"></i>
                        </a>
                        @endif
                        @if($settings['line_id'])
                        <a href="https://line.me/ti/p/{{ $settings['line_id'] }}" class="btn btn-sm btn-outline-success" target="_blank">
                            <i class="fab fa-line"></i>
                        </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        

    </div>
</div>
@endsection

@section('scripts')
<script>
// Live preview
document.addEventListener('DOMContentLoaded', function() {
    const inputs = {
        'site_name': 'preview-site-name',
        'site_description': 'preview-site-description',
        'contact_phone': 'preview-phone',
        'contact_email': 'preview-email',
        'contact_address': 'preview-address'
    };
    
    Object.keys(inputs).forEach(inputId => {
        const input = document.getElementById(inputId);
        const preview = document.getElementById(inputs[inputId]);
        
        if (input && preview) {
            input.addEventListener('input', function() {
                preview.textContent = this.value || 'ไม่ได้ระบุ';
            });
        }
    });
});
</script>
</div> <!-- ปิด content-safe-area -->
@endsection
