# Data Dictionary - ฐานข้อมูลผู้ใหญ่ประจักษ์ เซอร์วิส ช็อป

ตารางที่ 3.1 ตารางผู้ดูแลระบบ (users)

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสผู้ดูแลระบบ |
| 2 | name | VARCHAR(255) | - | ชื่อผู้ดูแลระบบ |
| 3 | email | VARCHAR(255) | UNIQUE | อีเมลสำหรับเข้าสู่ระบบ |
| 4 | password | VARCHAR(255) | - | รหัสผ่าน |
| 5 | created_at | TIMESTAMP | - | วันที่สร้างบัญชี |
| 6 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |

ตารางที่ 3.2 ตารางบริการ (services)

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสบริการ |
| 2 | title | VARCHAR(255) | - | ชื่อบริการ |
| 3 | description | TEXT | - | คำอธิบายบริการ |
| 4 | details | TEXT | - | รายละเอียดเพิ่มเติม |
| 5 | image | VARCHAR(255) | - | รูปภาพหลักของบริการ |
| 6 | is_active | BOOLEAN | - | สถานะการใช้งาน |
| 7 | sort_order | INT | - | ลำดับการแสดงผล |
| 8 | created_at | TIMESTAMP | - | วันที่สร้าง |
| 9 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |

ตารางที่ 3.3 ตารางรูปภาพบริการ (service_images)

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสรูปภาพ |
| 2 | service_id | BIGINT | FK | รหัสของบริการที่รูปนี้ใช้แสดง |
| 3 | image_path | VARCHAR(255) | - | ที่อยู่ไฟล์รูปภาพ |
| 4 | description | TEXT | - | คำอธิบายรูปภาพ |
| 5 | is_cover | BOOLEAN | - | รูปหน้าปก |
| 6 | sort_order | INT | - | ลำดับการแสดงผล |
| 7 | created_at | TIMESTAMP | - | วันที่อัปโหลด |
| 8 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |

ตารางที่ 3.4 ตารางแพ็คเกจบริการ (packages)

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสแพ็คเกจ |
| 2 | name | VARCHAR(255) | - | ชื่อแพ็คเกจ |
| 3 | description | TEXT | - | คำอธิบายแพ็คเกจ |
| 4 | features | TEXT | - | คุณสมบัติ/รายการ |
| 5 | price_text | VARCHAR(255) | - | ข้อความราคา |
| 6 | duration | VARCHAR(255) | - | ระยะเวลา |
| 7 | image | VARCHAR(255) | - | รูปภาพแพ็คเกจ |
| 8 | is_featured | BOOLEAN | - | แพ็คเกจแนะนำ |
| 9 | is_active | BOOLEAN | - | สถานะการใช้งาน |
| 10 | sort_order | INT | - | ลำดับการแสดงผล |
| 11 | created_at | TIMESTAMP | - | วันที่สร้าง |
| 12 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |

ตารางที่ 3.5 ตารางผลงาน (activities)

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสผลงาน |
| 2 | title | VARCHAR(255) | - | ชื่อผลงาน |
| 3 | description | TEXT | - | คำอธิบายผลงาน |
| 4 | details | TEXT | - | รายละเอียดเพิ่มเติม |
| 5 | image | VARCHAR(255) | - | รูปภาพหลัก |
| 6 | activity_date | DATE | - | วันที่จัดงาน |
| 7 | location | VARCHAR(255) | - | สถานที่จัดงาน |
| 8 | is_active | BOOLEAN | - | สถานะการแสดงผล |
| 9 | sort_order | INT | - | ลำดับการแสดงผล |
| 10 | created_at | TIMESTAMP | - | วันที่สร้าง |
| 11 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |

ตารางที่ 3.6 ตารางรูปภาพผลงาน (activity_images)

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสรูปภาพ |
| 2 | activity_id | BIGINT | FK | รหัสของผลงาน |
| 3 | image_path | VARCHAR(255) | - | ที่อยู่ไฟล์รูปภาพ |
| 4 | description | TEXT | - | คำอธิบายรูปภาพ |
| 5 | is_cover | BOOLEAN | - | รูปหน้าปก |
| 6 | sort_order | INT | - | ลำดับการแสดงผล |
| 7 | created_at | TIMESTAMP | - | วันที่อัปโหลด |
| 8 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |

ตารางที่ 3.7 ตารางการติดต่อ (contacts)

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสการติดต่อ |
| 2 | name | VARCHAR(255) | - | ชื่อผู้ติดต่อ |
| 3 | email | VARCHAR(255) | - | อีเมลผู้ติดต่อ |
| 4 | phone | VARCHAR(255) | - | เบอร์โทรศัพท์ |
| 5 | subject | VARCHAR(255) | - | หัวข้อการติดต่อ |
| 6 | message | TEXT | - | ข้อความ |
| 7 | is_read | BOOLEAN | - | สถานะการอ่าน |
| 8 | created_at | TIMESTAMP | - | วันที่ส่งข้อความ |
| 9 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |

ตารางที่ 3.8 ตารางแบนเนอร์ (banners)

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสแบนเนอร์ |
| 2 | title | VARCHAR(255) | - | ชื่อแบนเนอร์ |
| 3 | description | TEXT | - | คำอธิบายแบนเนอร์ |
| 4 | image_path | VARCHAR(255) | - | ที่อยู่ไฟล์รูปภาพ |
| 5 | display_pages | JSON | - | หน้าที่ต้องการแสดง |
| 6 | is_active | BOOLEAN | - | สถานะการแสดงผล |
| 7 | sort_order | INT | - | ลำดับการแสดงผล |
| 8 | created_at | TIMESTAMP | - | วันที่สร้าง |
| 9 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |

ตารางที่ 3.9 ตารางการตั้งค่าเว็บไซต์ (site_settings)

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|---------------|------|-----------|
| 1 | id | BIGINT | PK | รหัสการตั้งค่า |
| 2 | key | VARCHAR(255) | UNIQUE | คีย์การตั้งค่า |
| 3 | value | TEXT | - | ค่าการตั้งค่า |
| 4 | created_at | TIMESTAMP | - | วันที่สร้าง |
| 5 | updated_at | TIMESTAMP | - | วันที่แก้ไขล่าสุด |
