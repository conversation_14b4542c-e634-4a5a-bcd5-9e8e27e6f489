# 📝 คู่มือการแก้ไขโปรเจค - ผู้ใหญ่ประจักษ์ เซอร์วิส ช็อป

## 🎯 ภาพรวมโปรเจค

**ชื่อโปรเจค:** ผู้ใหญ่ประจักษ์ เซอร์วิส ช็อป  
**ประเภท:** เว็บไซต์ธุรกิจให้บริการจัดงานศพ  
**เทคโนโลยี:** Laravel 9.x + MySQL + Bootstrap 5  
**โครงสร้าง:** MV<PERSON> Pattern (Model-View-Controller)

---

## 🏗️ โครงสร้างโปรเจค

### 📁 โฟลเดอร์หลัก
```
PhuyaiPrajakserviceshop/
├── app/                    # โค้ด Backend (Laravel)
├── resources/views/        # ไฟล์ HTML Template
├── public/                 # ไฟล์ที่เข้าถึงได้จากเว็บ
├── database/              # ฐานข้อมูล Migration & Seeder
├── routes/                # การกำหนด URL
└── config/                # การตั้งค่าระบบ
```

### 🎨 ระบบหน้าบ้าน (Frontend)
- **หน้าหลัก** (`/`) - แสดงบริการ, ผลงาน, แบนเนอร์
- **หน้าบริการ** (`/services`) - แสดงบริการทั้งหมด
- **หน้าแพ็คเกจ** (`/packages`) - แสดงแพ็คเกจบริการ
- **หน้าผลงาน** (`/activities`) - แสดงผลงานการให้บริการ
- **หน้าติดต่อ** (`/contact`) - ฟอร์มติดต่อ

### 🔧 ระบบหลังบ้าน (Admin)
- **Dashboard** (`/admin`) - หน้าแรกของ Admin
- **จัดการบริการ** - เพิ่ม/แก้ไข/ลบบริการ
- **จัดการแพ็คเกจ** - จัดการแพ็คเกจบริการ
- **จัดการผลงาน** - จัดการผลงานและรูปภาพ
- **จัดการแบนเนอร์** - จัดการสไลด์โชว์
- **ตั้งค่าเว็บไซต์** - ข้อมูลติดต่อ, ชื่อเว็บไซต์

---

## 📊 ฐานข้อมูล

### ตารางหลัก (11 ตาราง)
1. **users** - ผู้ดูแลระบบ
2. **services** - บริการต่างๆ
3. **service_images** - รูปภาพบริการ
4. **packages** - แพ็คเกจบริการ
5. **activities** - ผลงานการให้บริการ
6. **activity_images** - รูปภาพผลงาน
7. **banners** - แบนเนอร์สไลด์โชว์
8. **contacts** - ข้อความจากลูกค้า
9. **site_settings** - การตั้งค่าเว็บไซต์
10. **service_categories** - หมวดหมู่บริการ
11. **migrations** - ประวัติการสร้างตาราง

---

## 🎨 การแก้ไขหน้าตาเว็บไซต์

### 1. 🖼️ การเปลี่ยนโลโก้
**ไฟล์ที่ต้องแก้:** `resources/views/layouts/app.blade.php`
**บรรทัดที่:** 130
```html
<img src="{{ asset('images/โลโก้ผู้ใหญ่ประจักษ์นกสีดำ.png') }}" alt="โลโก้หลัก">
```
**วิธีแก้:**
1. ใส่รูปโลโก้ใหม่ในโฟลเดอร์ `public/images/`
2. เปลี่ยนชื่อไฟล์ในโค้ด

### 2. 🎨 การเปลี่ยนสีธีม
**ไฟล์ที่ต้องแก้:** `public/css/funeral-style.css`
**ตัวอย่างการแก้สี:**
```css
:root {
    --primary-color: #2c3e50;    /* สีหลัก */
    --secondary-color: #34495e;  /* สีรอง */
    --accent-color: #e74c3c;     /* สีเน้น */
}
```

### 3. 📝 การเปลี่ยนฟอนต์
**ไฟล์ที่ต้องแก้:** `resources/css/app.css` หรือ `public/css/app.css`
```css
body {
    font-family: 'Sarabun', 'Kanit', sans-serif;
}
```

### 4. 🏠 การแก้ไขหน้าหลัก
**ไฟล์ที่ต้องแก้:** `resources/views/frontend/home.blade.php`
- **เปลี่ยนข้อความ:** แก้ไขข้อความใน HTML tags
- **เปลี่ยนรูปภาพ:** แก้ path ของรูปใน `src` attribute

---

## 📝 การแก้ไขเนื้อหา

### 1. 📄 การแก้ไขข้อความหน้าหลัก
**ไฟล์:** `resources/views/frontend/home.blade.php`
**ตำแหน่งสำคัญ:**
- **หัวข้อหลัก:** บรรทัด 20-30
- **คำอธิบายบริษัท:** บรรทัด 40-60
- **ข้อความ Call-to-Action:** บรรทัด 80-100

### 2. 🔧 การแก้ไขข้อมูลติดต่อ
**วิธีที่ 1:** ผ่าน Admin Panel
1. เข้า `/admin/login`
2. ไปที่ "ตั้งค่าเว็บไซต์"
3. แก้ไขข้อมูลติดต่อ

**วิธีที่ 2:** แก้ไขในฐานข้อมูล
**ตาราง:** `site_settings`
**ฟิลด์สำคัญ:**
- `site_name` - ชื่อเว็บไซต์
- `site_description` - คำอธิบายเว็บไซต์
- `contact_phone` - เบอร์โทรศัพท์
- `contact_email` - อีเมล
- `contact_address` - ที่อยู่

### 3. 🎯 การแก้ไขเมนูนำทาง
**ไฟล์:** `resources/views/layouts/app.blade.php`
**บรรทัดที่:** 139-155
```html
<ul class="navbar-nav ms-auto">
    <li class="nav-item">
        <a class="nav-link" href="{{ route('home') }}">หน้าหลัก</a>
    </li>
    <!-- เพิ่มเมนูใหม่ที่นี่ -->
</ul>
```

---

## 🛠️ การจัดการข้อมูล

### 1. 📋 การเพิ่มบริการใหม่
**ผ่าน Admin Panel:**
1. เข้า `/admin/services`
2. คลิก "เพิ่มบริการใหม่"
3. กรอกข้อมูล: ชื่อ, คำอธิบาย, รูปภาพ

**ผ่านฐานข้อมูล:**
**ตาราง:** `services`
**ฟิลด์สำคัญ:**
- `title` - ชื่อบริการ
- `description` - คำอธิบายสั้น
- `details` - รายละเอียดเต็ม
- `image` - รูปภาพหลัก
- `is_active` - สถานะ (1=เปิด, 0=ปิด)
- `sort_order` - ลำดับการแสดง

### 2. 🎨 การจัดการแบนเนอร์
**ไฟล์ที่เกี่ยวข้อง:**
- **Controller:** `app/Http/Controllers/BannerController.php`
- **Model:** `app/Models/Banner.php`
- **View:** `resources/views/admin/banners/`

**ตาราง:** `banners`
**ฟิลด์สำคัญ:**
- `title` - ชื่อแบนเนอร์
- `image` - รูปภาพ
- `link_url` - ลิงก์ (ถ้ามี)
- `page_location` - หน้าที่แสดง
- `is_active` - สถานะ
- `sort_order` - ลำดับ

### 3. 📸 การจัดการรูปภาพ
**โฟลเดอร์รูปภาพ:** `public/storage/`
**ประเภทรูปภาพ:**
- `banners/` - รูปแบนเนอร์
- `services/` - รูปบริการ
- `activities/` - รูปผลงาน
- `packages/` - รูปแพ็คเกจ

---

## 🔧 การแก้ไขระบบ Backend

### 1. 🎛️ Controller (ตัวควบคุม)
**ไฟล์สำคัญ:**
- `app/Http/Controllers/HomeController.php` - หน้าบ้าน
- `app/Http/Controllers/AdminController.php` - หลังบ้าน
- `app/Http/Controllers/AuthController.php` - ระบบล็อกอิน

### 2. 📊 Model (โมเดลข้อมูล)
**ไฟล์สำคัญ:**
- `app/Models/Service.php` - บริการ
- `app/Models/Package.php` - แพ็คเกจ
- `app/Models/Activity.php` - ผลงาน
- `app/Models/Banner.php` - แบนเนอร์

### 3. 🛣️ Routes (เส้นทาง URL)
**ไฟล์:** `routes/web.php`
**การเพิ่ม Route ใหม่:**
```php
Route::get('/หน้าใหม่', [Controller::class, 'method'])->name('route.name');
```

---

## 🎨 การปรับแต่ง CSS

### 1. 📱 Responsive Design
**ไฟล์:** `public/css/funeral-style.css`
**Media Queries สำคัญ:**
```css
/* Mobile */
@media (max-width: 768px) {
    .container { padding: 10px; }
}

/* Tablet */
@media (max-width: 1024px) {
    .navbar { padding: 5px; }
}
```

### 2. 🎨 Custom Styles
**การเพิ่ม CSS ใหม่:**
1. แก้ไขไฟล์ `resources/css/app.css`
2. รัน `npm run dev` เพื่อ compile
3. หรือแก้ไขตรงๆ ใน `public/css/app.css`

---

## 🔍 การ Debug และแก้ไขปัญหา

### 1. 🐛 การดู Error Log
**ไฟล์:** `storage/logs/laravel.log`

### 2. 🔧 การ Clear Cache
```bash
php artisan cache:clear
php artisan config:clear
php artisan view:clear
```

### 3. 🗃️ การ Backup ฐานข้อมูล
**ก่อนแก้ไขอะไร ให้ Backup ก่อน:**
```sql
mysqldump -u username -p phuyai_prajak_service_shop > backup.sql
```

---

## 📋 Checklist การแก้ไข

### ✅ ก่อนแก้ไข
- [ ] Backup ฐานข้อมูล
- [ ] Backup ไฟล์โค้ด
- [ ] ทดสอบในสภาพแวดล้อม Development

### ✅ หลังแก้ไข
- [ ] ทดสอบการทำงานทุกหน้า
- [ ] ตรวจสอบ Responsive Design
- [ ] ทดสอบระบบ Admin
- [ ] ตรวจสอบ Performance

---

## 🆘 ติดต่อขอความช่วยเหลือ

หากมีปัญหาในการแก้ไข สามารถดูข้อมูลเพิ่มเติมได้ที่:
- **Laravel Documentation:** https://laravel.com/docs
- **Bootstrap Documentation:** https://getbootstrap.com/docs
- **ไฟล์ README.md** ในโปรเจค

---

## 📂 รายละเอียดไฟล์สำคัญ

### 🎨 ไฟล์ Layout หลัก
**ไฟล์:** `resources/views/layouts/app.blade.php`
**หน้าที่:** Template หลักของเว็บไซต์
**ส่วนสำคัญ:**
- บรรทัด 125-135: Navigation Bar
- บรรทัด 130: โลโก้เว็บไซต์
- บรรทัด 139-155: เมนูหลัก
- บรรทัด 180-220: Footer

### 🏠 ไฟล์หน้าหลัก
**ไฟล์:** `resources/views/frontend/home.blade.php`
**ส่วนสำคัญ:**
- บรรทัด 1-30: Hero Section (แบนเนอร์หลัก)
- บรรทัด 40-80: ส่วนแสดงบริการ
- บรรทัด 90-130: ส่วนแสดงผลงาน
- บรรทัด 140-180: ส่วนข้อมูลติดต่อ

### 🎛️ ไฟล์ Controller หลัก
**ไฟล์:** `app/Http/Controllers/HomeController.php`
**Methods สำคัญ:**
- `index()` - หน้าหลัก
- `services()` - หน้าบริการ
- `packages()` - หน้าแพ็คเกจ
- `activities()` - หน้าผลงาน
- `contact()` - หน้าติดต่อ

---

## 🎯 การแก้ไขแบบละเอียด

### 1. 📝 การเปลี่ยนข้อความหน้าหลัก

#### 🏷️ เปลี่ยนชื่อเว็บไซต์
**ไฟล์:** `resources/views/layouts/app.blade.php`
**บรรทัด:** 131
```html
<span>{{ $settings['site_name'] ?? 'ชื่อเว็บไซต์ใหม่' }}</span>
```

#### 📢 เปลี่ยนข้อความ Hero Section
**ไฟล์:** `resources/views/frontend/home.blade.php`
**ค้นหา:** `<h1 class="display-4 fw-bold mb-4">`
**แก้ไข:** เปลี่ยนข้อความในแท็ก h1

#### 📋 เปลี่ยนคำอธิบายบริการ
**ไฟล์:** `resources/views/frontend/home.blade.php`
**ค้นหา:** `<p class="lead mb-4">`
**แก้ไข:** เปลี่ยนข้อความในแท็ก p

### 2. 🎨 การเปลี่ยนสีและธีม

#### 🌈 เปลี่ยนสีหลัก
**ไฟล์:** `public/css/funeral-style.css`
**ค้นหา:** `:root` หรือ CSS Variables
```css
:root {
    --primary-color: #YOUR_COLOR;
    --secondary-color: #YOUR_COLOR;
    --text-color: #YOUR_COLOR;
}
```

#### 🎨 เปลี่ยนสี Navigation
**ไฟล์:** `resources/views/layouts/app.blade.php`
**บรรทัด:** 125
```html
<nav class="navbar navbar-expand-lg navbar-light bg-YOUR_COLOR">
```

#### 🔘 เปลี่ยนสีปุ่ม
**ไฟล์:** `public/css/app.css` หรือ `public/css/funeral-style.css`
```css
.btn-primary {
    background-color: #YOUR_COLOR;
    border-color: #YOUR_COLOR;
}
```

### 3. 🖼️ การจัดการรูปภาพ

#### 📸 เปลี่ยนรูปแบนเนอร์
**วิธีที่ 1:** ผ่าน Admin Panel
1. เข้า `/admin/banners`
2. แก้ไขแบนเนอร์ที่ต้องการ
3. อัปโหลดรูปใหม่

**วิธีที่ 2:** แก้ไขในโค้ด
**ไฟล์:** `resources/views/frontend/home.blade.php`
**ค้นหา:** `<img src=` ในส่วน banner

#### 🏢 เปลี่ยนรูปโลโก้
**ขั้นตอน:**
1. ใส่รูปใหม่ใน `public/images/`
2. แก้ไขไฟล์ `resources/views/layouts/app.blade.php` บรรทัด 130
3. เปลี่ยนชื่อไฟล์ในโค้ด

#### 🎯 เปลี่ยนรูป Favicon
**ไฟล์:** `public/favicon.ico`
**วิธี:** แทนที่ไฟล์ favicon.ico ด้วยรูปใหม่

### 4. 📱 การปรับ Responsive Design

#### 📱 Mobile Navigation
**ไฟล์:** `resources/views/layouts/app.blade.php`
**บรรทัด:** 133-137
```html
<button class="navbar-toggler" type="button" data-bs-toggle="collapse">
    <span class="navbar-toggler-icon"></span>
</button>
```

#### 💻 Desktop Layout
**ไฟล์:** `public/css/funeral-style.css`
```css
@media (min-width: 992px) {
    .container {
        max-width: 1200px;
    }
}
```

#### 📱 Mobile Layout
**ไฟล์:** `public/css/funeral-style.css`
```css
@media (max-width: 768px) {
    .navbar-brand img {
        height: 40px;
    }
    .display-4 {
        font-size: 2rem;
    }
}
```

---

## 🔧 การจัดการระบบ Admin

### 1. 🔐 การเข้าสู่ระบบ Admin
**URL:** `/admin/login`
**ไฟล์:** `resources/views/admin/login.blade.php`
**ข้อมูลเข้าสู่ระบบ:** ดูในตาราง `users`

### 2. 📊 Dashboard Admin
**ไฟล์:** `resources/views/admin/dashboard.blade.php`
**Controller:** `app/Http/Controllers/AdminController.php`
**ฟีเจอร์:**
- สถิติการใช้งาน
- ข้อมูลล่าสุด
- ลิงก์ไปยังส่วนจัดการต่างๆ

### 3. 📋 การจัดการเนื้อหา

#### 🛠️ จัดการบริการ
**URL:** `/admin/services`
**ไฟล์ View:** `resources/views/admin/services/`
**Controller:** `app/Http/Controllers/AdminController.php`
**ฟีเจอร์:**
- เพิ่ม/แก้ไข/ลบบริการ
- อัปโหลดรูปภาพ
- จัดลำดับการแสดงผล

#### 📦 จัดการแพ็คเกจ
**URL:** `/admin/packages`
**ไฟล์ View:** `resources/views/admin/packages/`
**ฟีเจอร์:**
- จัดการแพ็คเกจบริการ
- กำหนดราคา
- ตั้งค่าความโดดเด่น

#### 🎨 จัดการแบนเนอร์
**URL:** `/admin/banners`
**ไฟล์ View:** `resources/views/admin/banners/`
**ฟีเจอร์:**
- อัปโหลดแบนเนอร์
- กำหนดหน้าที่แสดง
- จัดลำดับ

#### 📸 จัดการผลงาน
**URL:** `/admin/activities`
**ไฟล์ View:** `resources/views/admin/activities/`
**ฟีเจอร์:**
- เพิ่มผลงานใหม่
- อัปโหลดรูปภาพหลายรูป
- กำหนดวันที่และสถานที่

### 4. ⚙️ ตั้งค่าเว็บไซต์
**URL:** `/admin/settings`
**ไฟล์:** `resources/views/admin/settings.blade.php`
**ข้อมูลที่แก้ไขได้:**
- ชื่อเว็บไซต์
- คำอธิบายเว็บไซต์
- ข้อมูลติดต่อ
- โซเชียลมีเดีย

---

## 🗃️ การจัดการฐานข้อมูล

### 1. 📊 ตารางสำคัญและการใช้งาน

#### 👥 ตาราง users
**การใช้งาน:** ระบบ Admin
**ฟิลด์สำคัญ:**
- `name` - ชื่อผู้ใช้
- `email` - อีเมล (ใช้ล็อกอิน)
- `password` - รหัสผ่าน (เข้ารหัส)
- `role` - บทบาท (admin, user)

#### 🛠️ ตาราง services
**การใช้งาน:** หน้าหลัก, หน้าบริการ
**ฟิลด์สำคัญ:**
- `title` - ชื่อบริการ
- `description` - คำอธิบายสั้น
- `details` - รายละเอียดเต็ม
- `image` - รูปภาพหลัก
- `is_active` - สถานะ (1=แสดง, 0=ซ่อน)
- `sort_order` - ลำดับการแสดง

#### 📦 ตาราง packages
**การใช้งาน:** หน้าแพ็คเกจ
**ฟิลด์สำคัญ:**
- `name` - ชื่อแพ็คเกจ
- `description` - คำอธิบาย
- `features` - คุณสมบัติ (JSON)
- `price_text` - ข้อความราคา
- `is_featured` - แพ็คเกจแนะนำ

#### 🎨 ตาราง activities
**การใช้งาน:** หน้าหลัก, หน้าผลงาน
**ฟิลด์สำคัญ:**
- `title` - ชื่อผลงาน
- `description` - คำอธิบาย
- `activity_date` - วันที่จัดงาน
- `location` - สถานที่

#### 🖼️ ตาราง banners
**การใช้งาน:** สไลด์โชว์ทุกหน้า
**ฟิลด์สำคัญ:**
- `title` - ชื่อแบนเนอร์
- `image` - รูปภาพ
- `link_url` - ลิงก์ (ถ้ามี)
- `page_location` - หน้าที่แสดง

### 2. 🔗 ความสัมพันธ์ระหว่างตาราง
- `services` → `service_images` (1 ต่อ หลาย)
- `activities` → `activity_images` (1 ต่อ หลาย)

### 3. 💾 การ Backup และ Restore
```sql
-- Backup
mysqldump -u username -p phuyai_prajak_service_shop > backup_$(date +%Y%m%d).sql

-- Restore
mysql -u username -p phuyai_prajak_service_shop < backup_file.sql
```

---

## 🚀 การ Deploy และ Maintenance

### 1. 📤 การอัปโหลดไฟล์
**ไฟล์ที่ต้องอัปโหลด:**
- โฟลเดอร์ `public/` ทั้งหมด
- ไฟล์ที่แก้ไขใน `resources/views/`
- ไฟล์ CSS/JS ที่แก้ไข

### 2. 🔄 การ Clear Cache
```bash
# ใน Terminal/Command Prompt
php artisan cache:clear
php artisan config:clear
php artisan view:clear
php artisan route:clear
```

### 3. 🔧 การ Compile Assets
```bash
# Development
npm run dev

# Production
npm run production
```

---

## 🆘 การแก้ไขปัญหาที่พบบ่อย

### 1. 🚫 หน้าเว็บไม่แสดง
**สาเหตุ:** Cache ไม่ถูกต้อง
**วิธีแก้:** Clear cache ทั้งหมด

### 2. 🖼️ รูปภาพไม่แสดง
**สาเหตุ:** Storage link ไม่ถูกต้อง
**วิธีแก้:**
```bash
php artisan storage:link
```

### 3. 🎨 CSS ไม่เปลี่ยน
**สาเหตุ:** Browser cache
**วิธีแก้:** Hard refresh (Ctrl+F5) หรือ clear browser cache

### 4. 🔐 ล็อกอิน Admin ไม่ได้
**สาเหตุ:** ข้อมูล user ไม่ถูกต้อง
**วิธีแก้:** ตรวจสอบตาราง `users` ในฐานข้อมูล

### 5. 📱 Mobile ไม่ responsive
**สาเหตุ:** CSS media queries
**วิธีแก้:** ตรวจสอบไฟล์ CSS และ viewport meta tag

---

## 📋 Checklist การทดสอบ

### ✅ ทดสอบหน้าบ้าน
- [ ] หน้าหลักโหลดได้ปกติ
- [ ] เมนูทำงานถูกต้อง
- [ ] รูปภาพแสดงครบ
- [ ] ลิงก์ทำงานได้
- [ ] ฟอร์มติดต่อส่งได้
- [ ] Responsive ใน Mobile

### ✅ ทดสอบหน้าหลังบ้าน
- [ ] ล็อกอิน Admin ได้
- [ ] Dashboard แสดงข้อมูลถูกต้อง
- [ ] เพิ่ม/แก้ไข/ลบข้อมูลได้
- [ ] อัปโหลดรูปภาพได้
- [ ] บันทึกการตั้งค่าได้

### ✅ ทดสอบประสิทธิภาพ
- [ ] หน้าเว็บโหลดเร็ว (< 3 วินาที)
- [ ] รูปภาพมีขนาดเหมาะสม
- [ ] ไม่มี Error ใน Console
- [ ] SEO Meta tags ครบถ้วน

---

*📝 เอกสารนี้สร้างขึ้นเพื่อช่วยในการแก้ไขและพัฒนาโปรเจค ผู้ใหญ่ประจักษ์ เซอร์วิส ช็อป อย่างละเอียดและครบถ้วน*
