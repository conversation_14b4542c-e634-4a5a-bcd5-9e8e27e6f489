<?php
/**
 * Script สำหรับลบ service_categories และ category_id ที่ไม่ได้ใช้งาน
 * รันไฟล์นี้ใน terminal: php remove_categories_script.php
 */

require_once 'vendor/autoload.php';

// โหลด Laravel environment
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

echo "🗑️ เริ่มลบ service_categories และ category_id\n";
echo "==========================================\n\n";

try {
    // ตรวจสอบว่ามีตารางหรือไม่
    $hasServiceCategories = Schema::hasTable('service_categories');
    $hasColumnCategoryId = Schema::hasColumn('services', 'category_id');
    
    echo "📊 สถานะปัจจุบัน:\n";
    echo "- ตาราง service_categories: " . ($hasServiceCategories ? "มี" : "ไม่มี") . "\n";
    echo "- คอลัมน์ category_id ในตาราง services: " . ($hasColumnCategoryId ? "มี" : "ไม่มี") . "\n\n";
    
    if (!$hasServiceCategories && !$hasColumnCategoryId) {
        echo "✅ ไม่มีอะไรต้องลบ - ระบบสะอาดแล้ว!\n";
        exit(0);
    }
    
    // ขั้นตอนที่ 1: ลบ Foreign Key และ Column category_id จากตาราง services
    if ($hasColumnCategoryId) {
        echo "🔧 ขั้นตอนที่ 1: ลบ category_id จากตาราง services\n";
        
        // ตรวจสอบข้อมูลที่อ้างอิง
        $servicesWithCategory = DB::table('services')->whereNotNull('category_id')->count();
        echo "📋 บริการที่มี category_id: {$servicesWithCategory} รายการ\n";
        
        if ($servicesWithCategory > 0) {
            echo "🧹 ลบการอ้างอิง category_id ในตาราง services...\n";
            DB::table('services')->whereNotNull('category_id')->update(['category_id' => null]);
            echo "✅ ลบการอ้างอิงแล้ว\n";
        }
        
        // ลบ Foreign Key Constraint
        echo "🔗 ลบ Foreign Key Constraint...\n";
        try {
            Schema::table('services', function ($table) {
                $table->dropForeign(['category_id']);
            });
            echo "✅ ลบ Foreign Key แล้ว\n";
        } catch (Exception $e) {
            echo "⚠️ ไม่พบ Foreign Key หรือลบไม่ได้: " . $e->getMessage() . "\n";
        }
        
        // ลบ Column category_id
        echo "🗑️ ลบคอลัมน์ category_id...\n";
        Schema::table('services', function ($table) {
            $table->dropColumn('category_id');
        });
        echo "✅ ลบคอลัมน์ category_id แล้ว\n\n";
    }
    
    // ขั้นตอนที่ 2: ลบตาราง service_categories
    if ($hasServiceCategories) {
        echo "🔧 ขั้นตอนที่ 2: ลบตาราง service_categories\n";
        
        $categoryCount = DB::table('service_categories')->count();
        echo "📊 จำนวนหมวดหมู่: {$categoryCount} รายการ\n";
        
        // สำรองข้อมูลก่อนลบ (ถ้ามีข้อมูล)
        if ($categoryCount > 0) {
            echo "💾 สำรองข้อมูลก่อนลบ...\n";
            $categories = DB::table('service_categories')->get();
            $backupData = json_encode($categories->toArray(), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            file_put_contents('service_categories_backup_' . date('Y-m-d_H-i-s') . '.json', $backupData);
            echo "✅ สำรองข้อมูลแล้ว\n";
        }
        
        // ลบตาราง
        echo "🗑️ ลบตาราง service_categories...\n";
        Schema::dropIfExists('service_categories');
        echo "✅ ลบตาราง service_categories แล้ว\n\n";
    }
    
    echo "🎉 ลบเสร็จสิ้น!\n";
    echo "==========================================\n";
    echo "✅ สิ่งที่ลบแล้ว:\n";
    if ($hasColumnCategoryId) echo "   - คอลัมน์ category_id จากตาราง services\n";
    if ($hasServiceCategories) echo "   - ตาราง service_categories\n";
    echo "\n📋 ตารางที่เหลือในฐานข้อมูล:\n";
    
    // แสดงตารางที่เหลือ
    $databaseName = config('database.connections.mysql.database');
    $remainingTables = DB::select("SHOW TABLES");
    $tableColumn = "Tables_in_{$databaseName}";
    
    foreach ($remainingTables as $table) {
        $tableName = $table->$tableColumn;
        $count = DB::table($tableName)->count();
        echo "   - {$tableName} ({$count} รายการ)\n";
    }
    
    echo "\n🎯 ตอนนี้ฐานข้อมูลมี " . count($remainingTables) . " ตาราง\n";
    
} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
    echo "📝 กรุณาตรวจสอบและลองใหม่\n";
    exit(1);
}

echo "\n📝 สิ่งที่ควรทำต่อไป:\n";
echo "1. ลบไฟล์ Migration ที่เกี่ยวข้อง\n";
echo "2. ลบไฟล์ Seeder ที่เกี่ยวข้อง\n";
echo "3. ลบไฟล์ Script อื่นๆ ที่ไม่ใช้\n";
echo "4. ทดสอบเว็บไซต์ว่ายังทำงานปกติ\n";
echo "5. อัปเดต Data Dictionary\n\n";

echo "🚀 เสร็จสิ้น! เว็บไซต์ของคุณพร้อมใช้งานแล้ว\n";
?>
