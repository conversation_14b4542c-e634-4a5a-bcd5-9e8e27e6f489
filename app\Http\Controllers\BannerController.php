<?php

namespace App\Http\Controllers;

use App\Models\Banner;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class BannerController extends Controller
{
    public function index()
    {
        $banners = Banner::orderBy('sort_order', 'asc')->orderBy('created_at', 'desc')->simplePaginate(15);
        return view('admin.banners.index', compact('banners'));
    }

    public function create()
    {
        return view('admin.banners.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'display_pages' => 'nullable|array',
            'display_pages.*' => 'in:home,services,packages,activities,contact',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean'
        ]);

        $data = $request->all();
        
        // จัดการการอัปโหลดรูปภาพ
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $imageName = time() . '_' . Str::random(10) . '.' . $image->getClientOriginalExtension();
            $imagePath = $image->storeAs('banners', $imageName, 'public');
            $data['image_path'] = $imagePath;
        }

        // ตั้งค่าเริ่มต้น
        $data['is_active'] = $request->has('is_active');
        $data['sort_order'] = $request->sort_order ?? 0;

        // จัดการลิงก์ตามประเภท
        if ($request->link_type === 'custom') {
            $data['link_url'] = $request->link_url;
        } else {
            $data['link_url'] = null;
        }

        Banner::create($data);

        return redirect()->route('admin.banners.index')->with('success', 'เพิ่มแบนเนอร์เรียบร้อยแล้ว');
    }

    public function edit(Banner $banner)
    {
        return view('admin.banners.edit', compact('banner'));
    }

    public function update(Request $request, Banner $banner)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'display_pages' => 'nullable|array',
            'display_pages.*' => 'in:home,services,packages,activities,contact',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean'
        ]);

        $data = $request->all();
        
        // จัดการอัปโหลดรูปภาพใหม่
        if ($request->hasFile('image')) {
            // ลบรูปเก่า
            if ($banner->image_path && Storage::disk('public')->exists($banner->image_path)) {
                Storage::disk('public')->delete($banner->image_path);
            }
            
            $image = $request->file('image');
            $imageName = time() . '_' . Str::random(10) . '.' . $image->getClientOriginalExtension();
            $imagePath = $image->storeAs('banners', $imageName, 'public');
            $data['image_path'] = $imagePath;
        }

        // ตั้งค่าเริ่มต้น
        $data['is_active'] = $request->has('is_active');
        $data['sort_order'] = $request->sort_order ?? 0;

        // จัดการลิงก์ตามประเภท
        if ($request->link_type === 'custom') {
            $data['link_url'] = $request->link_url;
        } else {
            $data['link_url'] = null;
        }

        $banner->update($data);

        return redirect()->route('admin.banners.index')->with('success', 'แก้ไขแบนเนอร์เรียบร้อยแล้ว');
    }

    public function destroy(Banner $banner)
    {
        // ลบรูปภาพ
        if ($banner->image_path && Storage::disk('public')->exists($banner->image_path)) {
            Storage::disk('public')->delete($banner->image_path);
        }

        $banner->delete();

        return redirect()->route('admin.banners.index')->with('success', 'ลบแบนเนอร์เรียบร้อยแล้ว');
    }
}
