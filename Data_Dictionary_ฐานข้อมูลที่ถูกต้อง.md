# 📊 Data Dictionary - ฐานข้อมูลผู้ใหญ่ประจักษ์ เซอร์วิส ช็อป

## 🎯 ภาพรวมฐานข้อมูล

**ชื่อฐานข้อมูล:** `phuyai_prajak_service_shop`
**จำนวนตารางหลัก:** 10 ตาราง
**ระบบจัดการฐานข้อมูล:** MySQL 8.0+
**Character Set:** utf8mb4_unicode_ci

---

## 📋 รายการตารางทั้งหมด

| ลำดับ | ชื่อตาราง | จำนวนฟิลด์ | Primary Key | หน้าที่หลัก |
|-------|-----------|------------|-------------|-------------|
| 1 | `users` | 6 | `id` (BIGINT) | ระบบ Admin |
| 2 | `services` | 8 | `id` (BIGINT) | บริการหลัก |
| 3 | `service_images` | 9 | `id` (BIGINT) | รูปภาพบริการ |
| 4 | `packages` | 10 | `id` (BIGINT) | แพ็คเกจบริการ |
| 5 | `activities` | 9 | `id` (BIGINT) | ผลงาน |
| 6 | `activity_images` | 9 | `id` (BIGINT) | รูปภาพผลงาน |
| 7 | `banners` | 9 | `id` (BIGINT) | แบนเนอร์ |
| 8 | `contacts` | 9 | `id` (BIGINT) | ข้อความลูกค้า |
| 9 | `site_settings` | 6 | `id` (BIGINT) | การตั้งค่า |
| 10 | `migrations` | 3 | `id` (INT) | ระบบ Laravel |

---

## 🔍 รายละเอียดแต่ละตาราง

### 1. 👤 ตาราง `users` - ผู้ดูแลระบบ

**วัตถุประสงค์:** เก็บข้อมูลผู้ดูแลระบบ Admin

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | ขนาด | คีย์ | Null | Default | คำอธิบาย |
|-------|-----------|---------------|------|------|------|---------|-----------|
| 1 | `id` | BIGINT | 20 | PK | NO | AUTO_INCREMENT | รหัสผู้ดูแลระบบ |
| 2 | `name` | VARCHAR | 255 | - | NO | - | ชื่อผู้ดูแลระบบ |
| 3 | `email` | VARCHAR | 255 | UNIQUE | NO | - | อีเมลสำหรับเข้าสู่ระบบ |
| 4 | `password` | VARCHAR | 255 | - | NO | - | รหัสผ่าน (เข้ารหัส bcrypt) |
| 5 | `created_at` | TIMESTAMP | - | - | YES | NULL | วันที่สร้างบัญชี |
| 6 | `updated_at` | TIMESTAMP | - | - | YES | NULL | วันที่แก้ไขล่าสุด |

**การใช้งาน:** ระบบ Authentication, Admin Panel

---

### 2. 🛠️ ตาราง `services` - บริการ

**วัตถุประสงค์:** เก็บข้อมูลบริการต่างๆ ที่ให้บริการ

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | ขนาด | คีย์ | Null | Default | คำอธิบาย |
|-------|-----------|---------------|------|------|------|---------|-----------|
| 1 | `id` | BIGINT | 20 | PK | NO | AUTO_INCREMENT | รหัสบริการ |
| 2 | `title` | VARCHAR | 255 | - | NO | - | ชื่อบริการ |
| 3 | `description` | TEXT | - | - | NO | - | คำอธิบายบริการ |
| 4 | `details` | TEXT | - | - | YES | NULL | รายละเอียดเพิ่มเติม |
| 5 | `image` | VARCHAR | 255 | - | YES | NULL | รูปภาพหลักของบริการ |
| 6 | `is_active` | BOOLEAN | - | - | NO | 1 | สถานะการใช้งาน |
| 7 | `sort_order` | INT | 11 | - | NO | 0 | ลำดับการแสดงผล |
| 8 | `created_at` | TIMESTAMP | - | - | YES | NULL | วันที่สร้าง |
| 9 | `updated_at` | TIMESTAMP | - | - | YES | NULL | วันที่แก้ไขล่าสุด |

**การใช้งาน:** หน้าหลัก (3 บริการ), หน้าบริการทั้งหมด

---

### 3. 🖼️ ตาราง `service_images` - รูปภาพบริการ

**วัตถุประสงค์:** เก็บรูปภาพของแต่ละบริการ (Multiple Images)

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | ขนาด | คีย์ | Null | Default | คำอธิบาย |
|-------|-----------|---------------|------|------|------|---------|-----------|
| 1 | `id` | BIGINT | 20 | PK | NO | AUTO_INCREMENT | รหัสรูปภาพ |
| 2 | `service_id` | BIGINT | 20 | FK | NO | - | รหัสบริการที่เป็นเจ้าของรูป |
| 3 | `image_path` | VARCHAR | 255 | - | NO | - | ที่อยู่ไฟล์รูปภาพ |
| 4 | `alt_text` | VARCHAR | 255 | - | YES | NULL | ข้อความทดแทนรูปภาพ |
| 5 | `description` | TEXT | - | - | YES | NULL | คำอธิบายรูปภาพ |
| 6 | `is_cover` | BOOLEAN | - | - | NO | 0 | รูปหน้าปก (1=ใช่, 0=ไม่ใช่) |
| 7 | `sort_order` | INT | 11 | - | NO | 0 | ลำดับการแสดงผล |
| 8 | `created_at` | TIMESTAMP | - | - | YES | NULL | วันที่อัปโหลด |
| 9 | `updated_at` | TIMESTAMP | - | - | YES | NULL | วันที่แก้ไขล่าสุด |

**Foreign Key:** `service_id` → `services.id`  
**Relationship:** One-to-Many (1 บริการ : หลายรูป)

---

### 4. 📦 ตาราง `packages` - แพ็คเกจบริการ

**วัตถุประสงค์:** เก็บข้อมูลแพ็คเกจบริการและราคา

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | ขนาด | คีย์ | Null | Default | คำอธิบาย |
|-------|-----------|---------------|------|------|------|---------|-----------|
| 1 | `id` | BIGINT | 20 | PK | NO | AUTO_INCREMENT | รหัสแพ็คเกจ |
| 2 | `name` | VARCHAR | 255 | - | NO | - | ชื่อแพ็คเกจ |
| 3 | `description` | TEXT | - | - | NO | - | คำอธิบายแพ็คเกจ |
| 4 | `features` | TEXT | - | - | NO | - | คุณสมบัติ/รายการ |
| 5 | `price_text` | VARCHAR | 255 | - | YES | NULL | ข้อความราคา |
| 6 | `duration` | VARCHAR | 255 | - | YES | NULL | ระยะเวลา |
| 7 | `image` | VARCHAR | 255 | - | YES | NULL | รูปภาพแพ็คเกจ |
| 8 | `is_featured` | BOOLEAN | - | - | NO | 0 | แพ็คเกจแนะนำ |
| 9 | `is_active` | BOOLEAN | - | - | NO | 1 | สถานะการใช้งาน |
| 10 | `sort_order` | INT | 11 | - | NO | 0 | ลำดับการแสดงผล |
| 11 | `created_at` | TIMESTAMP | - | - | YES | NULL | วันที่สร้าง |
| 12 | `updated_at` | TIMESTAMP | - | - | YES | NULL | วันที่แก้ไขล่าสุด |

**การใช้งาน:** หน้าแพ็คเกจ, หน้ารายละเอียดแพ็คเกจ

---

### 5. 🎨 ตาราง `activities` - ผลงาน

**วัตถุประสงค์:** เก็บข้อมูลผลงานการให้บริการ

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | ขนาด | คีย์ | Null | Default | คำอธิบาย |
|-------|-----------|---------------|------|------|------|---------|-----------|
| 1 | `id` | BIGINT | 20 | PK | NO | AUTO_INCREMENT | รหัสผลงาน |
| 2 | `title` | VARCHAR | 255 | - | NO | - | ชื่อผลงาน |
| 3 | `description` | TEXT | - | - | NO | - | คำอธิบายผลงาน |
| 4 | `details` | TEXT | - | - | YES | NULL | รายละเอียดเพิ่มเติม |
| 5 | `image` | VARCHAR | 255 | - | YES | NULL | รูปภาพหลัก |
| 6 | `activity_date` | DATE | - | - | YES | NULL | วันที่จัดงาน |
| 7 | `location` | VARCHAR | 255 | - | YES | NULL | สถานที่จัดงาน |
| 8 | `is_active` | BOOLEAN | - | - | NO | 1 | สถานะการแสดงผล |
| 9 | `sort_order` | INT | 11 | - | NO | 0 | ลำดับการแสดงผล |
| 10 | `created_at` | TIMESTAMP | - | - | YES | NULL | วันที่สร้าง |
| 11 | `updated_at` | TIMESTAMP | - | - | YES | NULL | วันที่แก้ไขล่าสุด |

**การใช้งาน:** หน้าหลัก (4 ผลงานแบบสุ่ม), หน้าผลงานทั้งหมด

---

### 6. 📸 ตาราง `activity_images` - รูปภาพผลงาน

**วัตถุประสงค์:** เก็บรูปภาพของแต่ละผลงาน (Multiple Images)

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | ขนาด | คีย์ | Null | Default | คำอธิบาย |
|-------|-----------|---------------|------|------|------|---------|-----------|
| 1 | `id` | BIGINT | 20 | PK | NO | AUTO_INCREMENT | รหัสรูปภาพ |
| 2 | `activity_id` | BIGINT | 20 | FK | NO | - | รหัสผลงานที่เป็นเจ้าของรูป |
| 3 | `image_path` | VARCHAR | 255 | - | NO | - | ที่อยู่ไฟล์รูปภาพ |
| 4 | `alt_text` | VARCHAR | 255 | - | YES | NULL | ข้อความทดแทนรูปภาพ |
| 5 | `description` | TEXT | - | - | YES | NULL | คำอธิบายรูปภาพ |
| 6 | `is_cover` | BOOLEAN | - | - | NO | 0 | รูปหน้าปก |
| 7 | `sort_order` | INT | 11 | - | NO | 0 | ลำดับการแสดงผล |
| 8 | `created_at` | TIMESTAMP | - | - | YES | NULL | วันที่อัปโหลด |
| 9 | `updated_at` | TIMESTAMP | - | - | YES | NULL | วันที่แก้ไขล่าสุด |

**Foreign Key:** `activity_id` → `activities.id`  
**Relationship:** One-to-Many (1 ผลงาน : หลายรูป)

---

### 7. 🎨 ตาราง `banners` - แบนเนอร์

**วัตถุประสงค์:** เก็บข้อมูลแบนเนอร์สไลด์โชว์

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | ขนาด | คีย์ | Null | Default | คำอธิบาย |
|-------|-----------|---------------|------|------|------|---------|-----------|
| 1 | `id` | BIGINT | 20 | PK | NO | AUTO_INCREMENT | รหัสแบนเนอร์ |
| 2 | `title` | VARCHAR | 255 | - | NO | - | ชื่อแบนเนอร์ |
| 3 | `description` | TEXT | - | - | YES | NULL | คำอธิบายแบนเนอร์ |
| 4 | `image_path` | VARCHAR | 255 | - | NO | - | ที่อยู่ไฟล์รูปภาพ |
| 5 | `display_pages` | JSON | - | - | YES | NULL | หน้าที่ต้องการแสดง |
| 6 | `is_active` | BOOLEAN | - | - | NO | 1 | สถานะการแสดงผล |
| 7 | `sort_order` | INT | 11 | - | NO | 0 | ลำดับการแสดงผล |
| 8 | `created_at` | TIMESTAMP | - | - | YES | NULL | วันที่สร้าง |
| 9 | `updated_at` | TIMESTAMP | - | - | YES | NULL | วันที่แก้ไขล่าสุด |

**การใช้งาน:** สไลด์โชว์ในทุกหน้าของเว็บไซต์

---

### 8. 📞 ตาराง `contacts` - การติดต่อ

**วัตถุประสงค์:** เก็บข้อความติดต่อจากลูกค้า

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | ขนาด | คีย์ | Null | Default | คำอธิบาย |
|-------|-----------|---------------|------|------|------|---------|-----------|
| 1 | `id` | BIGINT | 20 | PK | NO | AUTO_INCREMENT | รหัสการติดต่อ |
| 2 | `name` | VARCHAR | 255 | - | NO | - | ชื่อผู้ติดต่อ |
| 3 | `email` | VARCHAR | 255 | - | NO | - | อีเมลผู้ติดต่อ |
| 4 | `phone` | VARCHAR | 20 | - | YES | NULL | เบอร์โทรศัพท์ |
| 5 | `subject` | VARCHAR | 255 | - | NO | - | หัวข้อการติดต่อ |
| 6 | `message` | TEXT | - | - | NO | - | ข้อความ |
| 7 | `is_read` | BOOLEAN | - | - | NO | 0 | สถานะการอ่าน |
| 8 | `created_at` | TIMESTAMP | - | - | YES | NULL | วันที่ส่งข้อความ |
| 9 | `updated_at` | TIMESTAMP | - | - | YES | NULL | วันที่แก้ไขล่าสุด |

**การใช้งาน:** ระบบ Admin ดูข้อความจากลูกค้า

---

### 9. ⚙️ ตาราง `site_settings` - การตั้งค่าเว็บไซต์

**วัตถุประสงค์:** เก็บการตั้งค่าต่างๆ ของเว็บไซต์

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | ขนาด | คีย์ | Null | Default | คำอธิบาย |
|-------|-----------|---------------|------|------|------|---------|-----------|
| 1 | `id` | BIGINT | 20 | PK | NO | AUTO_INCREMENT | รหัสการตั้งค่า |
| 2 | `key` | VARCHAR | 255 | UNIQUE | NO | - | คีย์การตั้งค่า |
| 3 | `value` | TEXT | - | - | YES | NULL | ค่าการตั้งค่า |
| 4 | `description` | TEXT | - | - | YES | NULL | คำอธิบายการตั้งค่า |
| 5 | `created_at` | TIMESTAMP | - | - | YES | NULL | วันที่สร้าง |
| 6 | `updated_at` | TIMESTAMP | - | - | YES | NULL | วันที่แก้ไขล่าสุด |

**ตัวอย่างข้อมูล:**
- `site_name` = "ผู้ใหญ่ประจักษ์ เซอร์วิส ช็อป"
- `contact_phone` = "02-xxx-xxxx"
- `contact_email` = "<EMAIL>"

---

### 10.  ตาราง `migrations` - ระบบ Laravel

**วัตถุประสงค์:** เก็บประวัติการสร้าง/แก้ไขโครงสร้างฐานข้อมูล

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | ขนาด | คีย์ | Null | Default | คำอธิบาย |
|-------|-----------|---------------|------|------|------|---------|-----------|
| 1 | `id` | INT | 10 | PK | NO | AUTO_INCREMENT | รหัสลำดับการรัน |
| 2 | `migration` | VARCHAR | 255 | - | NO | - | ชื่อไฟล์ migration |
| 3 | `batch` | INT | 11 | - | NO | - | กลุ่มการรัน migration |

**⚠️ สำคัญ:** ห้ามลบหรือแก้ไข - เป็นตารางระบบของ Laravel

---

## 🔗 ความสัมพันธ์ระหว่างตาราง (Relationships)

### 📊 One-to-Many Relationships

1. **services** → **service_images** (1:N)
   - Foreign Key: `service_images.service_id` → `services.id`
   - 1 บริการมีได้หลายรูปภาพ

2. **activities** → **activity_images** (1:N)
   - Foreign Key: `activity_images.activity_id` → `activities.id`
   - 1 ผลงานมีได้หลายรูปภาพ

### 🔄 Independent Tables (ไม่มีความสัมพันธ์)
- `users` - ตารางอิสระ
- `services` - ตารางอิสระ
- `packages` - ตารางอิสระ
- `activities` - ตารางอิสระ
- `contacts` - ตารางอิสระ
- `banners` - ตารางอิสระ
- `site_settings` - ตารางอิสระ
- `migrations` - ตารางระบบ

---

## ✅ สรุปการตรวจสอบ ID

### 🎯 **ผลการตรวจสอบ: ไม่มี ID ซ้ำกัน**

**เหตุผล:**
1. **แต่ละตารางมี Primary Key เป็นของตัวเอง** - ไม่มีการซ้ำกัน
2. **ใช้ AUTO_INCREMENT** - ระบบจัดการ ID อัตโนมัติ
3. **Foreign Key อ้างอิงถูกต้อง** - ชี้ไปยัง Primary Key ของตารางอื่น
4. **ตั้งชื่อฟิลด์ชัดเจน** - เช่น `service_id`, `activity_id`

### 📋 **รายการ Primary Key ทั้งหมด**
- `users.id` - รหัสผู้ดูแลระบบ
- `services.id` - รหัสบริการ
- `service_images.id` - รหัสรูปภาพบริการ
- `packages.id` - รหัสแพ็คเกจ
- `activities.id` - รหัสผลงาน
- `activity_images.id` - รหัสรูปภาพผลงาน
- `banners.id` - รหัสแบนเนอร์
- `contacts.id` - รหัสการติดต่อ
- `site_settings.id` - รหัสการตั้งค่า
- `migrations.id` - รหัส migration

**✅ สรุป: ฐานข้อมูลออกแบบถูกต้อง ไม่มีปัญหา ID ซ้ำกัน**

---

*📊 Data Dictionary นี้สร้างขึ้นจากการตรวจสอบโครงสร้างฐานข้อมูลจริง เพื่อความถูกต้องและครบถ้วน*
