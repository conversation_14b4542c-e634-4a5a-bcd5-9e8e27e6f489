<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Service;
use App\Models\ServiceImage;

class ServiceSeeder extends Seeder
{
    public function run()
    {
        // Create services (ลบการใช้ category แล้ว)
        $services = [
            [
                'title' => 'บริการจัดงานศพแบบครบวงจร',
                'description' => 'บริการจัดงานศพที่ครอบคลุมทุกขั้นตอน ตั้งแต่การเตรียมการจนถึงการส่งศพ',
                'details' => 'รวมถึงการจัดหาโลงศพ การแต่งหน้าผู้เสียชีวิต การจัดดอกไม้ การจัดเต็นท์ การจัดเสียง การจัดอาหาร และการประสานงานกับวัด ทุกอย่างครบครันในราคาที่เหมาะสม',
                'image' => 'services/service1.jpg',
                'is_active' => true,
                'sort_order' => 1
            ],
            [
                'title' => 'บริการรับจัดงานบุญ',
                'description' => 'รับจัดงานบุญต่างๆ เช่น งานบวช งานแต่งงาน งานขึ้นบ้านใหม่',
                'details' => 'ทีมงานมืออาชีพพร้อมให้คำปรึกษาและจัดงานให้เหมาะสมกับงบประมาณ มีอุปกรณ์ครบครัน เสียงดี อาหารอร่อย',
                'image' => 'services/service2.jpg',
                'is_active' => true,
                'sort_order' => 2
            ],
            [
                'title' => 'บริการเช่าเต็นท์และอุปกรณ์',
                'description' => 'ให้เช่าเต็นท์ โต๊ะ เก้าอี้ และอุปกรณ์งานต่างๆ',
                'details' => 'มีเต็นท์หลากหลายขนาด โต๊ะจีน โต๊ะกลม เก้าอี้พลาสติก เก้าอี้ชิวารี่ ระบบเสียง ไฟฟ้า และอุปกรณ์อื่นๆ ครบครัน',
                'image' => 'services/service3.jpg',
                'is_active' => true,
                'sort_order' => 3
            ],
            [
                'title' => 'บริการจัดเลี้ยง',
                'description' => 'รับจัดเลี้ยงงานต่างๆ ทั้งในและนอกสถานที่',
                'details' => 'มีเชฟมืออาชีพ อาหารสดใหม่ รสชาติอร่อย เมนูหลากหลาย ราคาเป็นกันเอง สามารถปรับเมนูตามความต้องการได้',
                'image' => 'services/service4.jpg',
                'is_active' => true,
                'sort_order' => 4
            ],
            [
                'title' => 'บริการขนส่งและรถเช่า',
                'description' => 'บริการรถเช่าและขนส่งสำหรับงานต่างๆ',
                'details' => 'มีรถตู้ รถบัส รถกระบะ สำหรับรับส่งแขก ขนของ หรือใช้ในงานต่างๆ พร้อมคนขับที่มีประสบการณ์',
                'image' => 'services/service5.jpg',
                'is_active' => true,
                'sort_order' => 5
            ],
            [
                'title' => 'บริการดนตรีและการแสดง',
                'description' => 'บริการวงดนตรี นักร้อง และการแสดงต่างๆ',
                'details' => 'มีวงดนตรีหลากหลายแนว นักร้องมืออาชีพ การแสดงพื้นบ้าน และการแสดงอื่นๆ เพื่อเพิ่มความสนุกสนานให้กับงาน',
                'image' => 'services/service6.jpg',
                'is_active' => true,
                'sort_order' => 6
            ]
        ];

        foreach ($services as $serviceData) {
            $service = Service::firstOrCreate(
                ['title' => $serviceData['title']],
                $serviceData
            );

            // Create sample images for each service
            $this->createServiceImages($service);
        }
    }

    private function createServiceImages($service)
    {
        // Create placeholder images for demonstration
        $imageCount = rand(2, 5); // Random number of images between 2-5
        
        for ($i = 1; $i <= $imageCount; $i++) {
            ServiceImage::firstOrCreate([
                'service_id' => $service->id,
                'sort_order' => $i
            ], [
                'image_path' => "services/sample-{$service->id}-{$i}.jpg",
                'alt_text' => $service->title . " - รูปที่ {$i}",
                'description' => "ตัวอย่างงาน {$service->title} รูปที่ {$i}",
                'is_cover' => $i === 1, // First image is cover
            ]);
        }
    }
}
