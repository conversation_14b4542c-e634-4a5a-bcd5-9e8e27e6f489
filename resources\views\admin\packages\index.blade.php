@extends('layouts.admin')

@section('title', 'จัดการแพ็คเกจ - ระบบจัดการ')

@section('breadcrumb')
<li class="breadcrumb-item active">จัดการแพ็คเกจ</li>
@endsection

@section('content')
<div class="content-safe-area">
<!-- Header Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                    <i class="fas fa-box me-2"></i>จัดการแพ็คเกจ
                </h1>
                <p class="text-muted mb-0">จัดการแพ็คเกจทั้งหมดของคุณ</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary" onclick="toggleView()">
                    <i class="fas fa-th-large me-2" id="viewIcon"></i>
                    <span id="viewText">มุมมองการ์ด</span>
                </button>
                <a href="{{ route('admin.packages.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>เพิ่มแพ็คเกจใหม่
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="row g-4 mb-4">
    <div class="col-md-4">
        <div class="card stats-card h-100">
            <div class="card-body text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">แพ็คเกจทั้งหมด</h6>
                        <h3 class="mb-0">{{ $totalPackages }}</h3>
                    </div>
                    <i class="fas fa-box fa-2x text-white-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100" style="background: linear-gradient(135deg, var(--success-color), #059669);">
            <div class="card-body text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">แพ็คเกจที่เปิดใช้</h6>
                        <h3 class="mb-0">{{ $activePackages }}</h3>
                    </div>
                    <i class="fas fa-check-circle fa-2x text-white-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100" style="background: linear-gradient(135deg, var(--warning-color), #d97706);">
            <div class="card-body text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">แพ็คเกจแนะนำ</h6>
                        <h3 class="mb-0">{{ $featuredPackages }}</h3>
                    </div>
                    <i class="fas fa-star fa-2x text-white-50"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Packages Content -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-list me-2"></i>รายการแพ็คเกจ
            </h5>
            <div class="d-flex gap-2">
                <div class="input-group" style="width: 300px;">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" placeholder="ค้นหาแพ็คเกจ..." id="searchInput">
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        @if($packages->count() > 0)

        <!-- Table View -->
        <div id="tableView">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th style="width: 80px;">รูปภาพ</th>
                            <th>ชื่อแพ็คเกจ</th>
                            <th>คำอธิบาย</th>
                            <th style="width: 100px;">ราคา</th>
                            <th style="width: 120px;">ระยะเวลา</th>
                            <th style="width: 100px;">สถานะ</th>
                            <th style="width: 80px;">แนะนำ</th>
                            <th style="width: 80px;">ลำดับ</th>
                            <th style="width: 150px;">การจัดการ</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($packages as $package)
                        <tr class="package-row">
                            <td>
                                @if($package->image)
                                <img src="{{ asset('storage/' . $package->image) }}" alt="{{ $package->name }}"
                                     class="img-thumbnail rounded" style="width: 60px; height: 60px; object-fit: cover;">
                                @else
                                <div class="bg-secondary bg-opacity-10 d-flex align-items-center justify-content-center rounded"
                                     style="width: 60px; height: 60px;">
                                    <i class="fas fa-box text-muted"></i>
                                </div>
                                @endif
                            </td>
                            <td>
                                <div>
                                    <strong class="package-name">{{ $package->name }}</strong>
                                    <br>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ $package->created_at->format('d/m/Y') }}
                                    </small>
                                </div>
                            </td>
                            <td>
                                <span class="package-description">{{ Str::limit($package->description, 80) }}</span>
                            </td>
                            <td>
                                @if($package->price_text)
                                <span class="text-success fw-bold">{{ $package->price_text }}</span>
                                @else
                                <span class="text-muted">สอบถามราคา</span>
                                @endif
                            </td>
                            <td>
                                @if($package->duration)
                                <span class="badge bg-info">{{ $package->duration }}</span>
                                @else
                                <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                @if($package->is_active)
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>เปิดใช้
                                </span>
                                @else
                                <span class="badge bg-secondary">
                                    <i class="fas fa-times me-1"></i>ปิดใช้
                                </span>
                                @endif
                            </td>
                            <td>
                                @if($package->is_featured)
                                <span class="badge bg-warning text-dark">
                                    <i class="fas fa-star me-1"></i>แนะนำ
                                </span>
                                @else
                                <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-info">{{ $package->sort_order ?? 0 }}</span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.packages.edit', $package->id) }}{{ request()->has('page') ? '?page=' . request('page') : '' }}"
                                       class="btn btn-sm btn-outline-primary" title="แก้ไข">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.packages.delete', $package->id) }}"
                                          method="POST" class="d-inline" id="deletePackageTableForm{{ $package->id }}">
                                        @csrf
                                        @method('DELETE')
                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                onclick="handleDeletePackageTable({{ $package->id }})" title="ลบ">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Card View -->
        <div id="cardView" style="display: none;">
            <div class="row g-4">
                @foreach($packages as $package)
                <div class="col-md-6 col-lg-4 package-card">
                    <div class="card h-100 interactive-card">
                        @if($package->image)
                        <img src="{{ asset('storage/' . $package->image) }}" class="card-img-top"
                             style="height: 200px; object-fit: cover;" alt="{{ $package->name }}">
                        @else
                        <div class="card-img-top bg-secondary bg-opacity-10 d-flex align-items-center justify-content-center"
                             style="height: 200px;">
                            <i class="fas fa-box fa-3x text-muted"></i>
                        </div>
                        @endif

                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h5 class="card-title package-name">{{ $package->name }}</h5>
                                @if($package->is_featured)
                                <span class="badge bg-warning text-dark">
                                    <i class="fas fa-star me-1"></i>แนะนำ
                                </span>
                                @endif
                            </div>
                            <p class="card-text package-description">{{ Str::limit($package->description, 100) }}</p>

                            @if($package->duration)
                            <div class="mb-3">
                                <span class="badge bg-info">{{ $package->duration }}</span>
                            </div>
                            @endif

                            <div class="d-flex justify-content-between align-items-center">
                                @if($package->is_active)
                                <span class="badge bg-success">เปิดใช้</span>
                                @else
                                <span class="badge bg-secondary">ปิดใช้</span>
                                @endif
                                <small class="text-muted">ลำดับ: {{ $package->sort_order ?? 0 }}</small>
                            </div>
                        </div>

                        <div class="card-footer bg-transparent">
                            <div class="d-flex gap-2">
                                <a href="{{ route('admin.packages.edit', $package->id) }}{{ request()->has('page') ? '?page=' . request('page') : '' }}"
                                   class="btn btn-primary flex-fill">
                                    <i class="fas fa-edit me-2"></i>แก้ไข
                                </a>
                                <form action="{{ route('admin.packages.delete', $package->id) }}"
                                      method="POST" class="flex-fill" id="deletePackageCardForm{{ $package->id }}">
                                    @csrf
                                    @method('DELETE')
                                    <button type="button" class="btn btn-danger w-100"
                                            onclick="handleDeletePackageCard({{ $package->id }})">
                                        <i class="fas fa-trash me-2"></i>ลบ
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>

        <!-- Pagination -->
        @if($packages->hasPages())
        <div class="mt-4 p-3 bg-light rounded">
            @include('custom.simple-pagination', ['paginator' => $packages])
        </div>
        @endif

        @else
        <div class="text-center py-5">
            <i class="fas fa-box fa-5x text-muted mb-4"></i>
            <h4 class="text-muted mb-2">ยังไม่มีแพ็คเกจ</h4>
            <p class="text-muted mb-4">เริ่มต้นด้วยการเพิ่มแพ็คเกจแรกของคุณ</p>
            <a href="{{ route('admin.packages.create') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-plus me-2"></i>เพิ่มแพ็คเกจใหม่
            </a>
        </div>
        @endif
    </div>
</div>
@endsection

@section('scripts')
<script>
let currentView = 'table';

function toggleView() {
    const tableView = document.getElementById('tableView');
    const cardView = document.getElementById('cardView');
    const viewIcon = document.getElementById('viewIcon');
    const viewText = document.getElementById('viewText');

    if (currentView === 'table') {
        tableView.style.display = 'none';
        cardView.style.display = 'block';
        viewIcon.className = 'fas fa-list me-2';
        viewText.textContent = 'มุมมองตาราง';
        currentView = 'card';
    } else {
        tableView.style.display = 'block';
        cardView.style.display = 'none';
        viewIcon.className = 'fas fa-th-large me-2';
        viewText.textContent = 'มุมมองการ์ด';
        currentView = 'table';
    }
}

// Search functionality
document.getElementById('searchInput').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const packageRows = document.querySelectorAll('.package-row');
    const packageCards = document.querySelectorAll('.package-card');

    // Search in table view
    packageRows.forEach(row => {
        const name = row.querySelector('.package-name').textContent.toLowerCase();
        const description = row.querySelector('.package-description').textContent.toLowerCase();

        if (name.includes(searchTerm) || description.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });

    // Search in card view
    packageCards.forEach(card => {
        const name = card.querySelector('.package-name').textContent.toLowerCase();
        const description = card.querySelector('.package-description').textContent.toLowerCase();

        if (name.includes(searchTerm) || description.includes(searchTerm)) {
            card.style.display = '';
        } else {
            card.style.display = 'none';
        }
    });
});

// Add animation to cards
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.interactive-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in-up');
    });
});

// Delete package function with custom modal (for table view)
async function handleDeletePackageTable(packageId) {
    const confirmed = await confirmDelete(
        'คุณแน่ใจหรือไม่ที่จะลบแพ็คเกจนี้? การกระทำนี้ไม่สามารถยกเลิกได้',
        'ยืนยันการลบแพ็คเกจ'
    );

    if (confirmed) {
        document.getElementById(`deletePackageTableForm${packageId}`).submit();
    }
}

// Delete package function with custom modal (for card view)
async function handleDeletePackageCard(packageId) {
    const confirmed = await confirmDelete(
        'คุณแน่ใจหรือไม่ที่จะลบแพ็คเกจนี้? การกระทำนี้ไม่สามารถยกเลิกได้',
        'ยืนยันการลบแพ็คเกจ'
    );

    if (confirmed) {
        document.getElementById(`deletePackageCardForm${packageId}`).submit();
    }
}
</script>
</div> <!-- ปิด content-safe-area -->
@endsection
