<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Service;
use App\Models\Package;
use App\Models\Contact;
use App\Models\Activity;
use App\Models\ActivityImage;
use App\Models\SiteSetting;
use Illuminate\Support\Facades\Storage;

class AdminController extends Controller
{
    public function dashboard()
    {
        $stats = [
            'services_count' => Service::count(),
            'packages_count' => Package::count(),
            'activities_count' => Activity::count(),
            'contacts_count' => Contact::count(),
            'unread_contacts' => Contact::unread()->count(),
        ];

        $recent_contacts = Contact::latest()->take(3)->get();

        // แชร์จำนวนข้อความที่ยังไม่อ่านกับ view ทั้งหมด
        view()->share('unread_contacts', $stats['unread_contacts']);

        return view('admin.dashboard', compact('stats', 'recent_contacts'));
    }

    // Services Management
    public function services()
    {
        $services = Service::ordered()->paginate(9); // 9 items per page

        // Get total counts for stats cards
        $totalServices = Service::count();
        $activeServices = Service::where('is_active', 1)->count();
        $inactiveServices = Service::where('is_active', 0)->count();

        return view('admin.services.index', compact('services', 'totalServices', 'activeServices', 'inactiveServices'));
    }

    public function createService()
    {
        return view('admin.services.create');
    }

    public function storeService(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'details' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'sort_order' => 'integer'
        ]);

        // จัดการข้อมูลโดยตรงเพื่อแก้ปัญหา checkbox
        $data = [
            'title' => $request->input('title'),
            'description' => $request->input('description'),
            'details' => $request->input('details'),
            'is_active' => $request->has('is_active') ? 1 : 0,
            'sort_order' => $request->input('sort_order', 0)
        ];

        // Set default image for backward compatibility
        $data['image'] = 'services/default.jpg';

        $service = Service::create($data);

        $currentOrder = 0;

        // Handle main image (will be the cover image)
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('services', 'public');
            $currentOrder++;

            \App\Models\ServiceImage::create([
                'service_id' => $service->id,
                'image_path' => $imagePath,
                'alt_text' => $request->input('title'),
                'description' => $request->input('title') . ' - รูปหลัก',
                'is_cover' => true,
                'sort_order' => $currentOrder
            ]);

            // Update service with main image path for backward compatibility
            $service->update(['image' => $imagePath]);
        }

        // Handle gallery images
        if ($request->hasFile('gallery_images')) {
            foreach ($request->file('gallery_images') as $index => $image) {
                $imagePath = $image->store('services', 'public');
                $currentOrder++;

                \App\Models\ServiceImage::create([
                    'service_id' => $service->id,
                    'image_path' => $imagePath,
                    'alt_text' => $request->input('title'),
                    'description' => $request->input('title') . ' - รูปที่ ' . $currentOrder,
                    'is_cover' => false,
                    'sort_order' => $currentOrder
                ]);
            }
        }

        $message = 'เพิ่มบริการเรียบร้อยแล้ว';
        if ($request->hasFile('gallery_images')) {
            $galleryCount = count($request->file('gallery_images'));
            $message .= ' (พร้อมรูปแกลเลอรี่ ' . $galleryCount . ' รูป)';
        }

        return redirect()->route('admin.services')->with('success', $message);
    }

    public function editService($id, Request $request)
    {
        $service = Service::with('images')->findOrFail($id);
        $page = $request->get('page', 1);
        return view('admin.services.edit', compact('service', 'page'));
    }

    public function updateService(Request $request, $id)
    {
        $service = Service::with('images')->findOrFail($id);

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'details' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'new_gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'sort_order' => 'integer'
        ]);

        // จัดการข้อมูลโดยตรงเพื่อแก้ปัญหา checkbox
        $data = [
            'title' => $request->input('title'),
            'description' => $request->input('description'),
            'details' => $request->input('details'),
            'is_active' => $request->has('is_active') ? 1 : 0,
            'sort_order' => $request->input('sort_order', 0)
        ];

        // Handle main image update
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('services', 'public');

            // Update or create cover image
            $coverImage = $service->images()->where('is_cover', true)->first();
            if ($coverImage) {
                // Delete old cover image file
                Storage::disk('public')->delete($coverImage->image_path);
                $coverImage->update([
                    'image_path' => $imagePath,
                    'alt_text' => $request->input('title'),
                    'description' => $request->input('title') . ' - รูปหลัก'
                ]);
            } else {
                // Create new cover image
                \App\Models\ServiceImage::create([
                    'service_id' => $service->id,
                    'image_path' => $imagePath,
                    'alt_text' => $request->input('title'),
                    'description' => $request->input('title') . ' - รูปหลัก',
                    'is_cover' => true,
                    'sort_order' => 1
                ]);
            }

            // Update service image for backward compatibility
            $data['image'] = $imagePath;
        }

        // Handle new gallery images
        if ($request->hasFile('new_gallery_images')) {
            $currentMaxOrder = $service->images()->max('sort_order') ?? 0;

            foreach ($request->file('new_gallery_images') as $index => $image) {
                $imagePath = $image->store('services', 'public');

                \App\Models\ServiceImage::create([
                    'service_id' => $service->id,
                    'image_path' => $imagePath,
                    'alt_text' => $request->input('title'),
                    'description' => $request->input('title') . ' - รูปที่ ' . ($currentMaxOrder + $index + 1),
                    'is_cover' => false,
                    'sort_order' => $currentMaxOrder + $index + 1
                ]);
            }
        }

        $service->update($data);

        $page = $request->get('page', 1);
        $message = 'แก้ไขบริการเรียบร้อยแล้ว';

        if ($request->hasFile('new_gallery_images')) {
            $galleryCount = count($request->file('new_gallery_images'));
            $message .= ' (เพิ่มรูปแกลเลอรี่ ' . $galleryCount . ' รูป)';
        }

        return redirect()->route('admin.services', ['page' => $page])->with('success', $message);
    }

    public function deleteService($id)
    {
        $service = Service::with('images')->findOrFail($id);

        // Delete all service images
        foreach ($service->images as $image) {
            Storage::disk('public')->delete($image->image_path);
        }
        $service->images()->delete();

        // Delete main image if exists
        if ($service->image) {
            Storage::disk('public')->delete($service->image);
        }

        $service->delete();

        return redirect()->route('admin.services')->with('success', 'ลบบริการเรียบร้อยแล้ว');
    }

    // Service Image Management
    public function deleteServiceImage($id)
    {
        $image = \App\Models\ServiceImage::findOrFail($id);
        $serviceId = $image->service_id;

        // Don't allow deleting cover image if it's the only image
        if ($image->is_cover) {
            $totalImages = \App\Models\ServiceImage::where('service_id', $serviceId)->count();
            if ($totalImages <= 1) {
                return response()->json([
                    'success' => false,
                    'message' => 'ไม่สามารถลบรูปปกได้ เนื่องจากเป็นรูปเดียวที่เหลือ'
                ], 400);
            }
        }

        // Delete image file
        Storage::disk('public')->delete($image->image_path);

        // If deleting cover image, set another image as cover
        if ($image->is_cover) {
            $newCover = \App\Models\ServiceImage::where('service_id', $serviceId)
                ->where('id', '!=', $id)
                ->orderBy('sort_order')
                ->first();

            if ($newCover) {
                $newCover->update(['is_cover' => true]);

                // Update service main image for backward compatibility
                $service = Service::find($serviceId);
                if ($service) {
                    $service->update(['image' => $newCover->image_path]);
                }
            }
        }

        $image->delete();

        return response()->json([
            'success' => true,
            'message' => 'ลบรูปภาพเรียบร้อยแล้ว'
        ]);
    }

    public function setServiceImageCover($id)
    {
        $image = \App\Models\ServiceImage::findOrFail($id);
        $serviceId = $image->service_id;

        // Remove cover status from all images of this service
        \App\Models\ServiceImage::where('service_id', $serviceId)
            ->update(['is_cover' => false]);

        // Set this image as cover
        $image->update(['is_cover' => true]);

        // Update service main image for backward compatibility
        $service = Service::find($serviceId);
        if ($service) {
            $service->update(['image' => $image->image_path]);
        }

        return response()->json([
            'success' => true,
            'message' => 'ตั้งเป็นรูปปกเรียบร้อยแล้ว'
        ]);
    }

    // Packages Management
    public function packages()
    {
        $packages = Package::ordered()->paginate(9); // 9 items per page

        // Get total counts for stats cards
        $totalPackages = Package::count();
        $activePackages = Package::where('is_active', 1)->count();
        $featuredPackages = Package::where('is_featured', 1)->count();

        return view('admin.packages.index', compact('packages', 'totalPackages', 'activePackages', 'featuredPackages'));
    }

    public function createPackage()
    {
        return view('admin.packages.create');
    }

    public function storePackage(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'features' => 'required|string',
            'price_text' => 'nullable|string|max:255',
            'duration' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'integer'
        ]);

        // จัดการข้อมูลโดยตรงเพื่อแก้ปัญหา checkbox
        $data = [
            'name' => $request->input('name'),
            'description' => $request->input('description'),
            'features' => $request->input('features'),
            'price_text' => $request->input('price_text'),
            'duration' => $request->input('duration'),
            'is_featured' => $request->has('is_featured') ? 1 : 0,
            'is_active' => $request->has('is_active') ? 1 : 0,
            'sort_order' => $request->input('sort_order', 0)
        ];

        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('packages', 'public');
        }

        Package::create($data);

        return redirect()->route('admin.packages')->with('success', 'เพิ่มแพ็คเกจเรียบร้อยแล้ว');
    }

    public function editPackage($id, Request $request)
    {
        $package = Package::findOrFail($id);
        $page = $request->get('page', 1);
        return view('admin.packages.edit', compact('package', 'page'));
    }

    public function updatePackage(Request $request, $id)
    {
        $package = Package::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'features' => 'required|string',
            'price_text' => 'nullable|string|max:255',
            'duration' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'integer'
        ]);

        // จัดการข้อมูลโดยตรงเพื่อแก้ปัญหา checkbox
        $data = [
            'name' => $request->input('name'),
            'description' => $request->input('description'),
            'features' => $request->input('features'),
            'price_text' => $request->input('price_text'),
            'duration' => $request->input('duration'),
            'is_featured' => $request->has('is_featured') ? 1 : 0,
            'is_active' => $request->has('is_active') ? 1 : 0,
            'sort_order' => $request->input('sort_order', 0)
        ];

        if ($request->hasFile('image')) {
            // Delete old image
            if ($package->image) {
                Storage::disk('public')->delete($package->image);
            }
            $data['image'] = $request->file('image')->store('packages', 'public');
        }

        $package->update($data);

        $page = $request->get('page', 1);
        return redirect()->route('admin.packages', ['page' => $page])->with('success', 'แก้ไขแพ็คเกจเรียบร้อยแล้ว');
    }

    public function deletePackage($id)
    {
        $package = Package::findOrFail($id);

        // Delete image
        if ($package->image) {
            Storage::disk('public')->delete($package->image);
        }

        $package->delete();

        return redirect()->route('admin.packages')->with('success', 'ลบแพ็คเกจเรียบร้อยแล้ว');
    }

    // การจัดการผลงาน (Activities Management)
    public function activities()
    {
        $activities = Activity::with('images')->ordered()->paginate(9); // 9 รายการต่อหน้า

        // ดึงจำนวนทั้งหมดสำหรับการ์ดสถิติ
        $totalActivities = Activity::count();
        $activeActivities = Activity::where('is_active', 1)->count();
        $thisMonthActivities = Activity::where('created_at', '>=', now()->startOfMonth())
                                      ->where('created_at', '<=', now()->endOfMonth())
                                      ->count();

        return view('admin.activities.index', compact('activities', 'totalActivities', 'activeActivities', 'thisMonthActivities'));
    }

    public function createActivity()
    {
        return view('admin.activities.create');
    }

    public function storeActivity(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'details' => 'nullable|string',
            'activity_date' => 'required|date',
            'location' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'sort_order' => 'integer'
        ]);

        // จัดการข้อมูลโดยตรงเพื่อแก้ปัญหา checkbox
        $data = [
            'title' => $request->input('title'),
            'description' => $request->input('description'),
            'details' => $request->input('details'),
            'activity_date' => $request->input('activity_date'),
            'location' => $request->input('location'),
            'is_active' => $request->has('is_active') ? 1 : 0,
            'sort_order' => $request->input('sort_order', 0)
        ];

        // Set default image for backward compatibility
        $data['image'] = 'activities/default.jpg';

        $activity = Activity::create($data);

        $currentOrder = 0;

        // Handle main image (will be the cover image)
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('activities', 'public');
            $currentOrder++;

            ActivityImage::create([
                'activity_id' => $activity->id,
                'image_path' => $imagePath,
                'alt_text' => $request->input('title'),
                'description' => $request->input('title') . ' - รูปหลัก',
                'is_cover' => true,
                'sort_order' => $currentOrder
            ]);

            // Update activity with main image path for backward compatibility
            $activity->update(['image' => $imagePath]);
        }

        // Handle gallery images
        if ($request->hasFile('gallery_images')) {
            foreach ($request->file('gallery_images') as $index => $image) {
                $imagePath = $image->store('activities', 'public');
                $currentOrder++;

                ActivityImage::create([
                    'activity_id' => $activity->id,
                    'image_path' => $imagePath,
                    'alt_text' => $request->input('title'),
                    'description' => $request->input('title') . ' - รูปที่ ' . $currentOrder,
                    'is_cover' => false,
                    'sort_order' => $currentOrder
                ]);
            }
        }

        $message = 'เพิ่มผลงานเรียบร้อยแล้ว';
        if ($request->hasFile('gallery_images')) {
            $galleryCount = count($request->file('gallery_images'));
            $message .= ' (พร้อมรูปแกลเลอรี่ ' . $galleryCount . ' รูป)';
        }

        return redirect()->route('admin.activities')->with('success', $message);
    }

    public function editActivity($id, Request $request)
    {
        $activity = Activity::with('images')->findOrFail($id);
        $page = $request->get('page', 1);
        return view('admin.activities.edit', compact('activity', 'page'));
    }

    public function updateActivity(Request $request, $id)
    {
        $activity = Activity::findOrFail($id);

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'details' => 'nullable|string',
            'activity_date' => 'required|date',
            'location' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'new_gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'sort_order' => 'integer'
        ]);

        // จัดการข้อมูลโดยตรงเพื่อแก้ปัญหา checkbox
        $data = [
            'title' => $request->input('title'),
            'description' => $request->input('description'),
            'details' => $request->input('details'),
            'activity_date' => $request->input('activity_date'),
            'location' => $request->input('location'),
            'is_active' => $request->has('is_active') ? 1 : 0,
            'sort_order' => $request->input('sort_order', 0)
        ];

        // Handle main image update
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('activities', 'public');

            // Update or create cover image
            $coverImage = $activity->images()->where('is_cover', true)->first();
            if ($coverImage) {
                // Delete old cover image file
                Storage::disk('public')->delete($coverImage->image_path);
                $coverImage->update([
                    'image_path' => $imagePath,
                    'alt_text' => $request->input('title'),
                    'description' => $request->input('title') . ' - รูปหลัก'
                ]);
            } else {
                // Create new cover image
                ActivityImage::create([
                    'activity_id' => $activity->id,
                    'image_path' => $imagePath,
                    'alt_text' => $request->input('title'),
                    'description' => $request->input('title') . ' - รูปหลัก',
                    'is_cover' => true,
                    'sort_order' => 1
                ]);
            }

            // Update activity image for backward compatibility
            $data['image'] = $imagePath;
        }

        // Handle new gallery images
        if ($request->hasFile('new_gallery_images')) {
            $currentMaxOrder = $activity->images()->max('sort_order') ?? 0;

            foreach ($request->file('new_gallery_images') as $index => $image) {
                $imagePath = $image->store('activities', 'public');

                ActivityImage::create([
                    'activity_id' => $activity->id,
                    'image_path' => $imagePath,
                    'alt_text' => $request->input('title'),
                    'description' => $request->input('title') . ' - รูปที่ ' . ($currentMaxOrder + $index + 1),
                    'is_cover' => false,
                    'sort_order' => $currentMaxOrder + $index + 1
                ]);
            }
        }

        $activity->update($data);

        $message = 'แก้ไขผลงานเรียบร้อยแล้ว';

        if ($request->hasFile('new_gallery_images')) {
            $galleryCount = count($request->file('new_gallery_images'));
            $message .= ' (เพิ่มรูปแกลเลอรี่ ' . $galleryCount . ' รูป)';
        }

        $page = $request->get('page', 1);
        return redirect()->route('admin.activities', ['page' => $page])->with('success', $message);
    }

    public function deleteActivity($id)
    {
        $activity = Activity::findOrFail($id);

        // Delete all image files
        foreach ($activity->images as $image) {
            Storage::disk('public')->delete($image->image_path);
        }

        // Delete old image file (backward compatibility)
        if ($activity->image) {
            Storage::disk('public')->delete($activity->image);
        }

        $activity->delete();

        return redirect()->route('admin.activities')->with('success', 'ลบผลงานเรียบร้อยแล้ว');
    }

    // Activity Images Management
    public function deleteActivityImage($id)
    {
        $image = ActivityImage::findOrFail($id);
        $activityId = $image->activity_id;

        // Don't allow deleting cover image if it's the only image
        if ($image->is_cover) {
            $totalImages = ActivityImage::where('activity_id', $activityId)->count();
            if ($totalImages <= 1) {
                return response()->json([
                    'success' => false,
                    'message' => 'ไม่สามารถลบรูปปกได้ เนื่องจากเป็นรูปเดียวที่เหลือ'
                ], 400);
            }
        }

        // Delete image file
        Storage::disk('public')->delete($image->image_path);

        // If deleting cover image, set another image as cover
        if ($image->is_cover) {
            $newCover = ActivityImage::where('activity_id', $activityId)
                ->where('id', '!=', $id)
                ->orderBy('sort_order')
                ->first();

            if ($newCover) {
                $newCover->update(['is_cover' => true]);

                // Update activity main image for backward compatibility
                $activity = Activity::find($activityId);
                if ($activity) {
                    $activity->update(['image' => $newCover->image_path]);
                }
            }
        }

        $image->delete();

        return response()->json([
            'success' => true,
            'message' => 'ลบรูปภาพเรียบร้อยแล้ว'
        ]);
    }

    public function setActivityImageCover($id)
    {
        $image = ActivityImage::findOrFail($id);
        $activityId = $image->activity_id;

        // Remove cover status from all images of this activity
        ActivityImage::where('activity_id', $activityId)
            ->update(['is_cover' => false]);

        // Set this image as cover
        $image->update(['is_cover' => true]);

        // Update activity main image for backward compatibility
        $activity = Activity::find($activityId);
        if ($activity) {
            $activity->update(['image' => $image->image_path]);
        }

        return response()->json([
            'success' => true,
            'message' => 'ตั้งเป็นรูปปกเรียบร้อยแล้ว'
        ]);
    }

    // Contacts Management
    public function contacts()
    {
        $contacts = Contact::latest()->get();
        return view('admin.contacts.index', compact('contacts'));
    }

    public function showContact($id)
    {
        $contact = Contact::findOrFail($id);

        // Mark as read
        if (!$contact->is_read) {
            $contact->update(['is_read' => true]);
        }

        return view('admin.contacts.show', compact('contact'));
    }

    public function deleteContact($id)
    {
        $contact = Contact::findOrFail($id);
        $contact->delete();

        return redirect()->route('admin.contacts')->with('success', 'ลบข้อความเรียบร้อยแล้ว');
    }

    // Site Settings
    public function settings()
    {
        $settings = [
            'site_name' => SiteSetting::get('site_name', ''),
            'site_description' => SiteSetting::get('site_description', ''),
            'contact_phone' => SiteSetting::get('contact_phone', ''),
            'contact_email' => SiteSetting::get('contact_email', ''),
            'contact_address' => SiteSetting::get('contact_address', ''),
            'facebook_url' => SiteSetting::get('facebook_url', ''),
            'line_id' => SiteSetting::get('line_id', ''),
        ];

        return view('admin.settings', compact('settings'));
    }

    public function updateSettings(Request $request)
    {
        $request->validate([
            'site_name' => 'required|string|max:255',
            'site_description' => 'required|string',
            'contact_phone' => 'required|string|max:20',
            'contact_email' => 'required|email|max:255',
            'contact_address' => 'required|string',
            'facebook_url' => 'nullable|url',
            'line_id' => 'nullable|string|max:255',
        ]);

        foreach ($request->all() as $key => $value) {
            if ($key !== '_token' && $key !== '_method') {
                SiteSetting::set($key, $value);
            }
        }

        return redirect()->route('admin.settings')->with('success', 'บันทึกการตั้งค่าเรียบร้อยแล้ว');
    }
}
