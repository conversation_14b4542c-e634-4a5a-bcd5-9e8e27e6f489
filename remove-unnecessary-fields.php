<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

// โหลด Laravel configuration
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🗑️ สคริปต์ลบฟิลด์ที่ไม่จำเป็นออกจากฐานข้อมูล\n";
echo "=================================================\n\n";

// ฟิลด์ที่จะลบ (ปลอดภัย)
$fieldsToRemove = [
    'users' => ['email_verified_at', 'remember_token'],
    'service_images' => ['alt_text'],
    'activity_images' => ['alt_text'],
    'site_settings' => ['type']
];

try {
    // ตรวจสอบการเชื่อมต่อฐานข้อมูล
    echo "🔍 ตรวจสอบการเชื่อมต่อฐานข้อมูล...\n";
    DB::connection()->getPdo();
    echo "✅ การเชื่อมต่อฐานข้อมูล: สำเร็จ\n\n";

    // แสดงฟิลด์ที่จะลบ
    echo "📋 ฟิลด์ที่จะลบ:\n";
    echo "----------------------------------------\n";
    foreach ($fieldsToRemove as $table => $fields) {
        echo "📊 ตาราง: {$table}\n";
        foreach ($fields as $field) {
            echo "   - {$field}\n";
        }
        echo "\n";
    }

    // ขอการยืนยัน
    echo "⚠️ คำเตือน: การลบฟิลด์ไม่สามารถกู้คืนได้!\n";
    echo "📝 ตรวจสอบให้แน่ใจว่าได้สำรองข้อมูลแล้ว\n\n";
    echo "❓ ต้องการดำเนินการต่อหรือไม่? (พิมพ์ 'YES' เพื่อยืนยัน): ";
    
    $handle = fopen("php://stdin", "r");
    $confirmation = trim(fgets($handle));
    fclose($handle);

    if ($confirmation !== 'YES') {
        echo "❌ ยกเลิกการดำเนินการ\n";
        exit(0);
    }

    echo "\n🚀 เริ่มดำเนินการลบฟิลด์...\n";
    echo "----------------------------------------\n";

    $removedCount = 0;
    $skippedCount = 0;

    foreach ($fieldsToRemove as $tableName => $fields) {
        echo "\n📊 ตาราง: {$tableName}\n";
        
        // ตรวจสอบว่าตารางมีอยู่หรือไม่
        if (!Schema::hasTable($tableName)) {
            echo "⏭️  ข้าม - ไม่พบตาราง {$tableName}\n";
            $skippedCount += count($fields);
            continue;
        }

        foreach ($fields as $fieldName) {
            try {
                // ตรวจสอบว่าฟิลด์มีอยู่หรือไม่
                if (Schema::hasColumn($tableName, $fieldName)) {
                    // ลบฟิลด์
                    Schema::table($tableName, function ($table) use ($fieldName) {
                        $table->dropColumn($fieldName);
                    });
                    
                    echo "   ✅ ลบฟิลด์ '{$fieldName}' สำเร็จ\n";
                    $removedCount++;
                } else {
                    echo "   ⏭️  ข้าม '{$fieldName}' - ไม่พบฟิลด์นี้\n";
                    $skippedCount++;
                }
            } catch (Exception $e) {
                echo "   ❌ ไม่สามารถลบฟิลด์ '{$fieldName}': " . $e->getMessage() . "\n";
                $skippedCount++;
            }
        }
    }

    echo "\n🎉 ดำเนินการเสร็จสิ้น!\n";
    echo "==========================================\n";
    echo "📊 สรุปผลการดำเนินการ:\n";
    echo "   ✅ ลบฟิลด์สำเร็จ: {$removedCount} ฟิลด์\n";
    echo "   ⏭️  ข้าม: {$skippedCount} ฟิลด์\n\n";

    if ($removedCount > 0) {
        echo "💾 แนะนำให้สำรองข้อมูลใหม่:\n";
        $databaseName = config('database.connections.mysql.database');
        $username = config('database.connections.mysql.username');
        echo "mysqldump -u {$username} -p {$databaseName} > backup_after_remove_fields_" . date('Y-m-d_H-i-s') . ".sql\n\n";
    }

    // ตรวจสอบโครงสร้างตารางที่แก้ไข
    echo "📋 ตรวจสอบโครงสร้างตารางที่แก้ไข:\n";
    echo "----------------------------------------\n";
    
    foreach ($fieldsToRemove as $tableName => $fields) {
        if (Schema::hasTable($tableName)) {
            echo "\n📊 ตาราง: {$tableName}\n";
            $columns = Schema::getColumnListing($tableName);
            echo "   ฟิลด์ที่เหลือ: " . implode(', ', $columns) . "\n";
        }
    }

    echo "\n✅ การลบฟิลด์เสร็จสมบูรณ์!\n";
    echo "🎯 ฐานข้อมูลตอนนี้เรียบง่ายขึ้นแล้ว\n\n";

} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
    echo "📍 ไฟล์: " . $e->getFile() . " บรรทัด: " . $e->getLine() . "\n\n";
    
    echo "🔧 วิธีแก้ไข:\n";
    echo "1. ตรวจสอบการตั้งค่าฐานข้อมูลในไฟล์ .env\n";
    echo "2. ตรวจสอบว่า MySQL Server ทำงานอยู่\n";
    echo "3. ตรวจสอบว่าฐานข้อมูลมีอยู่จริง\n";
    echo "4. ตรวจสอบสิทธิ์การเข้าถึงฐานข้อมูล\n\n";
    
    echo "📝 หากยังมีปัญหา สามารถลบด้วย SQL โดยตรง:\n";
    echo "ALTER TABLE users DROP COLUMN email_verified_at, DROP COLUMN remember_token;\n";
    echo "ALTER TABLE service_images DROP COLUMN alt_text;\n";
    echo "ALTER TABLE activity_images DROP COLUMN alt_text;\n";
    echo "ALTER TABLE site_settings DROP COLUMN type;\n";
}

echo "\n🏁 สคริปต์สิ้นสุด\n";
