# ⚡ Quick Reference - การแก้ไขด่วน

## 🎯 การแก้ไขที่พบบ่อยที่สุด

### 🖼️ เปลี่ยนโลโก้ (5 นาที)
1. ใส่รูปใหม่ใน `public/images/`
2. แก้ไข `resources/views/layouts/app.blade.php` บรรทัด 130
3. เปลี่ยนชื่อไฟล์ในโค้ด

### 🎨 เปลี่ยนสีเว็บไซต์ (10 นาที)
1. แก้ไข `public/css/funeral-style.css`
2. ค้นหา `:root` และเปลี่ยนสี
3. Clear cache: `php artisan cache:clear`

### 📝 เปลี่ยนข้อความหน้าหลัก (15 นาที)
1. แก้ไข `resources/views/frontend/home.blade.php`
2. ค้นหา `<h1>`, `<p>` ที่ต้องการเปลี่ยน
3. บันทึกและรีเฟรชหน้าเว็บ

### 📞 เปลี่ยนข้อมูลติดต่อ (5 นาที)
1. เข้า `/admin/login`
2. ไปที่ "ตั้งค่าเว็บไซต์"
3. แก้ไขและบันทึก

---

## 📁 ไฟล์สำคัญและหน้าที่

| ไฟล์ | หน้าที่ | ความสำคัญ |
|------|---------|-----------|
| `resources/views/layouts/app.blade.php` | Template หลัก, โลโก้, เมนู | ⭐⭐⭐⭐⭐ |
| `resources/views/frontend/home.blade.php` | หน้าหลัก | ⭐⭐⭐⭐⭐ |
| `public/css/funeral-style.css` | สไตล์หลัก | ⭐⭐⭐⭐ |
| `app/Http/Controllers/HomeController.php` | ตัวควบคุมหน้าบ้าน | ⭐⭐⭐ |
| `routes/web.php` | เส้นทาง URL | ⭐⭐⭐ |

---

## 🎨 สีธีมที่แนะนำ

### 🔵 ธีมสีฟ้า (เป็นทางการ)
```css
--primary-color: #3498db;
--secondary-color: #2980b9;
--accent-color: #e74c3c;
```

### 🟢 ธีมสีเขียว (ธรรมชาติ)
```css
--primary-color: #27ae60;
--secondary-color: #229954;
--accent-color: #f39c12;
```

### 🟣 ธีมสีม่วง (หรูหรา)
```css
--primary-color: #8e44ad;
--secondary-color: #7d3c98;
--accent-color: #e67e22;
```

### ⚫ ธีมสีเข้ม (คลาสสิก)
```css
--primary-color: #2c3e50;
--secondary-color: #34495e;
--accent-color: #e74c3c;
```

---

## 📱 Bootstrap Classes ที่ใช้บ่อย

### 🎨 สี (Colors)
- `bg-primary`, `bg-secondary`, `bg-success`, `bg-danger`
- `text-primary`, `text-white`, `text-dark`, `text-muted`

### 📏 ขนาด (Spacing)
- `p-1` ถึง `p-5` (padding)
- `m-1` ถึง `m-5` (margin)
- `py-3` (padding top/bottom), `px-4` (padding left/right)

### 📱 Grid System
- `col-12` (mobile), `col-md-6` (tablet), `col-lg-4` (desktop)
- `container`, `container-fluid`
- `row`, `justify-content-center`, `align-items-center`

### 🔘 ปุ่ม (Buttons)
- `btn btn-primary`, `btn btn-secondary`, `btn btn-outline-primary`
- `btn-sm`, `btn-lg`

---

## 🗃️ ฐานข้อมูล Quick Commands

### 👤 จัดการ Admin User
```sql
-- ดู Admin ทั้งหมด
SELECT * FROM users;

-- เปลี่ยนรหัสผ่าน (password = "password")
UPDATE users SET password = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi' WHERE id = 1;

-- สร้าง Admin ใหม่
INSERT INTO users (name, email, password, role, created_at, updated_at) 
VALUES ('Admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', NOW(), NOW());
```

### 🛠️ จัดการบริการ
```sql
-- ดูบริการทั้งหมด
SELECT id, title, is_active FROM services;

-- เปิด/ปิดบริการ
UPDATE services SET is_active = 1 WHERE id = 1;

-- เปลี่ยนลำดับ
UPDATE services SET sort_order = 1 WHERE id = 1;
```

### ⚙️ จัดการการตั้งค่า
```sql
-- ดูการตั้งค่าทั้งหมด
SELECT * FROM site_settings;

-- เปลี่ยนชื่อเว็บไซต์
UPDATE site_settings SET value = 'ชื่อใหม่' WHERE key = 'site_name';

-- เปลี่ยนเบอร์โทร
UPDATE site_settings SET value = '************' WHERE key = 'contact_phone';
```

---

## 🔧 Laravel Commands ที่ใช้บ่อย

### 🧹 Clear Cache
```bash
php artisan cache:clear
php artisan config:clear
php artisan view:clear
php artisan route:clear
```

### 🔗 Storage Link
```bash
php artisan storage:link
```

### 🗃️ Database
```bash
php artisan migrate
php artisan db:seed
php artisan migrate:fresh --seed
```

### 🛠️ Development
```bash
php artisan serve
php artisan tinker
```

---

## 📦 NPM Commands

### 🎨 Asset Compilation
```bash
npm run dev          # Development
npm run watch        # Watch for changes
npm run production   # Production (minified)
```

### 📥 Package Management
```bash
npm install          # ติดตั้ง packages
npm update           # อัปเดต packages
```

---

## 🚨 การแก้ไขปัญหาด่วน

### 🖼️ รูปภาพไม่แสดง
1. `php artisan storage:link`
2. ตรวจสอบ path ใน `<img src="">`
3. ตรวจสอบไฟล์อยู่ใน `public/storage/`

### 🎨 CSS ไม่เปลี่ยน
1. `npm run dev`
2. `php artisan cache:clear`
3. Hard refresh browser (Ctrl+F5)

### 🔐 ล็อกอิน Admin ไม่ได้
1. ตรวจสอบ email/password ในตาราง `users`
2. รีเซ็ตรหัสผ่านด้วย SQL
3. ตรวจสอบ session configuration

### 📱 Mobile ไม่ responsive
1. ตรวจสอบ viewport meta tag
2. ใช้ Bootstrap classes ที่ถูกต้อง
3. ทดสอบใน Chrome DevTools

### ⚡ เว็บไซต์ช้า
1. Optimize รูปภาพ (ลดขนาด)
2. `npm run production`
3. Enable caching
4. ตรวจสอบ database queries

---

## 📋 Checklist ก่อนส่งมอบงาน

### ✅ การทำงานพื้นฐาน
- [ ] หน้าหลักโหลดได้
- [ ] เมนูทำงานครบ
- [ ] รูปภาพแสดงครบ
- [ ] ฟอร์มติดต่อส่งได้
- [ ] Admin login ได้

### ✅ Responsive Design
- [ ] Mobile (320px-768px)
- [ ] Tablet (768px-1024px)
- [ ] Desktop (1024px+)

### ✅ Browser Compatibility
- [ ] Chrome
- [ ] Firefox
- [ ] Safari
- [ ] Edge

### ✅ Performance
- [ ] หน้าโหลดเร็ว (< 3 วินาที)
- [ ] รูปภาพ optimize แล้ว
- [ ] CSS/JS minify แล้ว

### ✅ SEO & Accessibility
- [ ] Title tags ครบ
- [ ] Meta descriptions ครบ
- [ ] Alt text ในรูปภาพ
- [ ] Heading structure ถูกต้อง

---

## 🆘 ติดต่อขอความช่วยเหลือ

### 📚 เอกสารอ้างอิง
- Laravel Docs: https://laravel.com/docs
- Bootstrap Docs: https://getbootstrap.com/docs
- PHP Manual: https://www.php.net/manual

### 🔍 การ Debug
1. ตรวจสอบ `storage/logs/laravel.log`
2. เปิด Browser Developer Tools
3. ตรวจสอบ Network tab
4. ดู Console errors

### 💡 Tips สำคัญ
- **Backup ก่อนแก้ไขเสมอ**
- **ทดสอบใน Development ก่อน**
- **Clear cache หลังแก้ไข**
- **ใช้ Git สำหรับ version control**

---

## 🎯 สรุปการแก้ไขแต่ละประเภท

| ประเภทการแก้ไข | ไฟล์หลัก | เวลาที่ใช้ | ความยาก |
|----------------|----------|-----------|---------|
| เปลี่ยนโลโก้ | `layouts/app.blade.php` | 5 นาที | ⭐ |
| เปลี่ยนสี | `funeral-style.css` | 10 นาที | ⭐⭐ |
| เปลี่ยนข้อความ | `home.blade.php` | 15 นาที | ⭐ |
| เพิ่มหน้าใหม่ | หลายไฟล์ | 1 ชั่วโมง | ⭐⭐⭐⭐ |
| แก้ไข Database | SQL/Admin Panel | 30 นาที | ⭐⭐⭐ |

---

*⚡ Quick Reference นี้ช่วยให้แก้ไขได้อย่างรวดเร็วและมีประสิทธิภาพ*
