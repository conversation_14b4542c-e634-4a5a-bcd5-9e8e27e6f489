<!DOCTYPE html>
<html lang="th" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'Phuyai Prajak Service Shop Admin')</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- AOS (Animate On Scroll) -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <!-- Custom Admin CSS -->
    <link href="{{ asset('css/admin-custom.css') }}" rel="stylesheet">
    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <style>
        /* ธีมสีสบายตา - Professional Clean Design */
        :root {
            /* สีหลัก */
            --primary-color: #3B82F6;
            --primary-hover: #2563EB;
            --primary-light: #DBEAFE;
            --secondary-color: #6366F1;
            --secondary-hover: #4F46E5;

            /* สีพื้นหลัง */
            --bg-primary: #F8FAFC;
            --bg-secondary: #F1F5F9;
            --bg-surface: #FFFFFF;
            --bg-hover: #F8FAFC;

            /* สีขอบ */
            --border-light: #E2E8F0;
            --border-medium: #CBD5E1;
            --border-dark: #94A3B8;

            /* สีข้อความ */
            --text-primary: #1E293B;
            --text-secondary: #64748B;
            --text-muted: #94A3B8;
            --text-white: #FFFFFF;

            /* สีสถานะ */
            --success-color: #10B981;
            --success-light: #D1FAE5;
            --warning-color: #F59E0B;
            --warning-light: #FEF3C7;
            --danger-color: #EF4444;
            --danger-light: #FEE2E2;
            --info-color: #06B6D4;
            --info-light: #CFFAFE;

            /* เงา */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        html, body {
            font-family: 'Inter', 'Kanit', sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: all 0.3s ease;
            line-height: 1.6;
            height: auto;
            overflow-x: hidden;
        }

        /* Navigation Bar */
        .top-navbar {
            background: linear-gradient(135deg, var(--bg-surface) 0%, var(--bg-secondary) 100%) !important;
            border-bottom: 1px solid var(--border-light);
            box-shadow: var(--shadow-sm);
            backdrop-filter: blur(10px);
        }

        .navbar-brand {
            color: var(--primary-color) !important;
            font-weight: 700;
            font-size: 1.5rem;
            text-decoration: none;
        }

        .navbar-brand:hover {
            color: var(--primary-hover) !important;
        }

        /* Navigation Links */
        .nav-link {
            color: var(--text-secondary) !important;
            font-weight: 500;
            border-radius: 10px;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link:hover {
            background: linear-gradient(135deg, var(--primary-light) 0%, rgba(59, 130, 246, 0.1) 100%) !important;
            color: var(--primary-color) !important;
            transform: translateY(-1px);
        }

        .nav-link.active {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
            color: var(--text-white) !important;
            box-shadow: var(--shadow-md);
        }

        /* Cards */
        .card {
            background-color: var(--bg-surface);
            border: 1px solid var(--border-light);
            border-radius: 16px;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
            color: var(--text-primary);
        }

        .card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
            border-color: var(--border-medium);
        }

        .card-header {
            background: linear-gradient(135deg, var(--bg-surface) 0%, var(--bg-secondary) 100%);
            border-bottom: 1px solid var(--border-light);
            border-radius: 16px 16px 0 0 !important;
        }

        /* Dropdown */
        .dropdown-menu {
            background-color: var(--bg-surface);
            border: 1px solid var(--border-light);
            border-radius: 12px;
            box-shadow: var(--shadow-lg);
            padding: 8px;
        }

        .dropdown-item {
            color: var(--text-secondary);
            border-radius: 8px;
            padding: 8px 12px;
            transition: all 0.3s ease;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, var(--primary-light) 0%, rgba(59, 130, 246, 0.1) 100%);
            color: var(--primary-color);
        }

        /* Layout */
        .top-navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            height: 70px;
            padding: 0.5rem 1rem;
        }

        .main-wrapper {
            margin-top: 85px; /* เพิ่มจาก 70px เป็น 85px เพื่อป้องกันการบัง */
            padding: 2rem;
            padding-top: 1rem; /* ลด padding-top เพื่อชดเชย margin-top ที่เพิ่มขึ้น */
            padding-bottom: 3rem;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            width: 100%;
            overflow-x: hidden;
        }

        /* เพิ่ม utility class สำหรับป้องกันการบัง */
        .content-safe-area {
            padding-top: 1rem;
            margin-top: 0.5rem;
        }

        /* ปรับปรุงสำหรับหน้าจอขนาดเล็ก */
        @media (max-width: 768px) {
            .main-wrapper {
                margin-top: 80px; /* ลดลงเล็กน้อยสำหรับมือถือ */
                padding-top: 0.5rem;
            }
        }

        /* Navigation Pills */
        .nav-pills .nav-link {
            border-radius: 10px;
            padding: 10px 16px;
            margin: 0 4px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        /* Buttons */
        .btn {
            border-radius: 10px;
            font-weight: 500;
            padding: 10px 20px;
            transition: all 0.3s ease;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: var(--text-white);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-hover) 0%, var(--secondary-hover) 100%);
            color: var(--text-white);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
            color: var(--text-white);
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #D97706 100%);
            color: var(--text-white);
        }

        .btn-info {
            background: linear-gradient(135deg, var(--info-color) 0%, #0891B2 100%);
            color: var(--text-white);
        }

        .btn-outline-primary {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            color: var(--text-white);
        }

        .btn-outline-secondary {
            border: 2px solid var(--border-medium);
            color: var(--text-secondary);
            background: transparent;
        }

        .btn-outline-secondary:hover {
            background: var(--text-secondary);
            color: var(--text-white);
        }

        /* Stats Cards */
        .stats-card {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: var(--text-white);
            border: none;
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        /* Alerts */
        .alert {
            border-radius: 12px;
            border: none;
            padding: 16px 20px;
            margin-bottom: 20px;
        }

        .alert-success {
            background: var(--success-light);
            color: var(--success-color);
            border-left: 4px solid var(--success-color);
        }

        .alert-danger {
            background: var(--danger-light);
            color: var(--danger-color);
            border-left: 4px solid var(--danger-color);
        }

        .alert-warning {
            background: var(--warning-light);
            color: var(--warning-color);
            border-left: 4px solid var(--warning-color);
        }

        .alert-info {
            background: var(--info-light);
            color: var(--info-color);
            border-left: 4px solid var(--info-color);
        }

        /* Form Controls */
        .form-control {
            border-radius: 10px;
            border: 2px solid var(--border-light);
            padding: 12px 16px;
            transition: all 0.3s ease;
            background-color: var(--bg-surface);
            color: var(--text-primary);
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
            background-color: var(--bg-surface);
            color: var(--text-primary);
        }

        .form-select {
            border-radius: 10px;
            border: 2px solid var(--border-light);
            padding: 12px 16px;
            background-color: var(--bg-surface);
            color: var(--text-primary);
        }

        /* Badge */
        .badge {
            border-radius: 8px;
            padding: 6px 12px;
            font-weight: 500;
            font-size: 0.75rem;
        }

        .bg-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
        }

        .bg-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%) !important;
        }

        .bg-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #D97706 100%) !important;
        }

        .bg-info {
            background: linear-gradient(135deg, var(--info-color) 0%, #0891B2 100%) !important;
        }

        .bg-danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, #DC2626 100%) !important;
        }

        /* List Group */
        .list-group-item {
            border: 1px solid var(--border-light);
            background-color: var(--bg-surface);
            color: var(--text-primary);
            border-radius: 10px !important;
            margin-bottom: 8px;
            transition: all 0.3s ease;
        }

        .list-group-item:hover {
            background-color: var(--bg-hover);
            transform: translateX(4px);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .top-navbar {
                height: 75px; /* เพิ่มความสูงเล็กน้อยสำหรับมือถือ */
            }

            .main-wrapper {
                margin-top: 85px; /* ปรับให้สอดคล้องกับความสูง navbar */
                padding: 1rem;
                padding-top: 0.5rem;
            }

            .nav-pills {
                flex-direction: column;
                gap: 0.5rem;
            }

            .card {
                margin-bottom: 1rem;
            }

            .btn {
                width: 100%;
                margin-bottom: 0.5rem;
            }

            /* เพิ่มการป้องกันการบังสำหรับมือถือ */
            .content-safe-area {
                padding-top: 1.5rem;
                margin-top: 1rem;
            }
        }

        /* Animations */
        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.02);
            }
        }

        .slide-in-right {
            animation: slideInRight 0.6s ease-out;
        }

        .pulse-hover:hover {
            animation: pulse 0.6s ease-in-out;
        }

        /* Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-secondary);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-medium);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }

        /* Ripple Effect */
        .btn {
            position: relative;
            overflow: hidden;
        }

        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* Loading State */
        .btn-loading {
            pointer-events: none;
            opacity: 0.7;
        }

        /* Ultra-smooth performance optimizations */
        * {
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        body {
            overflow-x: hidden;
            -webkit-overflow-scrolling: touch;
        }

        /* GPU acceleration for smooth animations */
        .card, .btn, .nav-link, .table {
            transform: translateZ(0);
            backface-visibility: hidden;
            perspective: 1000px;
        }

        /* Optimize transitions */
        .btn, .nav-link, .card {
            transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
            will-change: transform, opacity;
        }

        .btn:hover {
            transform: translateY(-1px) translateZ(0);
        }

        .btn:active {
            transform: translateY(0) translateZ(0);
            transition-duration: 0.05s;
        }

        /* Smooth table scrolling */
        .table-responsive {
            -webkit-overflow-scrolling: touch;
            scroll-behavior: smooth;
        }

        /* Optimize images */
        img {
            image-rendering: -webkit-optimize-contrast;
            image-rendering: crisp-edges;
        }

        /* Reduce layout shifts */
        .card {
            contain: layout style paint;
        }

        /* Smooth navigation */
        .nav-link {
            position: relative;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: var(--primary-color);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link.active::before,
        .nav-link:hover::before {
            width: 80%;
        }

        /* Optimize form controls */
        .form-control, .form-select {
            transition: border-color 0.15s ease, box-shadow 0.15s ease;
        }

        /* Smooth alerts */
        .alert {
            animation: slideInDown 0.3s ease;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Utility Classes */
        .text-gradient {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .border-gradient {
            border: 2px solid;
            border-image: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) 1;
        }

        .shadow-primary {
            box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.25);
        }

        .shadow-success {
            box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.25);
        }

        .shadow-warning {
            box-shadow: 0 4px 14px 0 rgba(245, 158, 11, 0.25);
        }

        .shadow-danger {
            box-shadow: 0 4px 14px 0 rgba(239, 68, 68, 0.25);
        }
    </style>

    @yield('styles')
</head>
<body>
    <!-- Page Loading Indicator (Disabled) -->
    <div id="pageLoader" class="loading-overlay" style="display: none !important;">
        <div class="text-center">
            <div class="loading-spinner"></div>
            <p class="mt-3 text-muted">กำลังโหลด...</p>
        </div>
    </div>

    <!-- Top Navigation -->
    <nav class="navbar navbar-expand-lg top-navbar">
        <div class="container-fluid">
            <!-- Brand -->
            <a class="navbar-brand" href="{{ route('admin.dashboard') }}">
            <img src="{{ asset('images/โลโก้ผู้ใหญ่ประจักษ์นกสีดำ.png') }}" alt="โลโก้หลัก" class="me-2" style="height: 60px; width: auto;">
                <i class=""></i>ผู้ใหญ่ประจักร์บริการ Admin
            </a>

            <!-- Mobile Toggle -->
            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Navigation Links -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav nav-pills me-auto">
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}" href="{{ route('admin.dashboard') }}">
                            <i class="fas fa-home me-2"></i>หน้าหลัก
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('admin.services*') ? 'active' : '' }}" href="{{ route('admin.services') }}">
                            <i class="fas fa-cogs me-2"></i>บริการ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('admin.packages*') ? 'active' : '' }}" href="{{ route('admin.packages') }}">
                            <i class="fas fa-box me-2"></i>แพ็คเกจ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('admin.activities*') ? 'active' : '' }}" href="{{ route('admin.activities') }}">
                            <i class="fas fa-images me-2"></i>ผลงาน
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('admin.banners*') ? 'active' : '' }}" href="{{ route('admin.banners.index') }}">
                            <i class="fas fa-image me-2"></i>แบนเนอร์
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('admin.contacts*') ? 'active' : '' }}" href="{{ route('admin.contacts') }}">
                            <i class="fas fa-envelope me-2"></i>ติดต่อเรา
                            @if(isset($unread_contacts) && $unread_contacts > 0)
                                <span class="badge bg-danger ms-1">{{ $unread_contacts }}</span>
                            @endif
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('admin.settings*') ? 'active' : '' }}" href="{{ route('admin.settings') }}">
                            <i class="fas fa-cog me-2"></i>ตั้งค่า
                        </a>
                    </li>
                </ul>

                <!-- Right Side -->
                <div class="d-flex align-items-center gap-3">
                    <!-- User Dropdown -->
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i>{{ Auth::user()->name }}
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{{ route('admin.services.create') }}">
                                <i class="fas fa-plus me-2"></i>เพิ่มบริการ
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('admin.packages.create') }}">
                                <i class="fas fa-plus me-2"></i>เพิ่มแพ็คเกจ
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('admin.activities.create') }}">
                                <i class="fas fa-plus me-2"></i>เพิ่มผลงาน
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ route('home') }}" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>ดูเว็บไซต์
                            </a></li>
                            <li>
                                <form action="{{ route('admin.logout') }}" method="POST" class="d-inline">
                                    @csrf
                                    <button type="submit" class="dropdown-item">
                                        <i class="fas fa-sign-out-alt me-2"></i>ออกจากระบบ
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content Wrapper -->
    <div class="main-wrapper">
        <!-- Alerts -->
        @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show fade-in-up" role="alert">
            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        @endif

        @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show fade-in-up" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        @endif

        @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show fade-in-up" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <ul class="mb-0">
                @foreach($errors->all() as $error)
                <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        @endif

        <!-- Page Content -->
        <div class="fade-in-up">
            @yield('content')
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom Admin JS -->
    <script src="{{ asset('js/admin-custom.js') }}"></script>
    <script src="{{ asset('js/admin-performance.js') }}"></script>
    <script src="{{ asset('js/admin-navbar-fix.js') }}"></script>

    <!-- Enhanced Admin JS -->
    <script>
        // Loading functions (Disabled for smooth experience)
        function hidePageLoader() {
            // Disabled - no loading overlay
            return;
        }

        function showPageLoader() {
            // Disabled - no loading overlay
            return;
        }

        function forceHideLoader() {
            // Disabled - no loading overlay
            return;
        }

        // เริ่มต้นระบบ
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Phuyai Prajak Service Shop Admin System Loaded');

            // Initialize AOS
            AOS.init({
                duration: 600,
                easing: 'ease-out-cubic',
                once: true,
                offset: 50,
                delay: 100
            });

            // Page loader disabled for smooth experience

            // เพิ่ม animation ให้ cards
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('fade-in-up');
            });

            // เพิ่ม hover effect ให้ navigation
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });

                link.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('active')) {
                        this.style.transform = 'translateY(0)';
                    }
                });
            });
        });

        // Advanced performance optimizations for ultra-smooth experience

        // 1. Instant page preloading on hover
        const adminLinks = document.querySelectorAll('a[href^="/admin"]');
        const preloadedPages = new Set();

        adminLinks.forEach(link => {
            link.addEventListener('mouseenter', function() {
                const href = this.href;
                if (!preloadedPages.has(href)) {
                    // Preload page instantly
                    const preloadLink = document.createElement('link');
                    preloadLink.rel = 'prefetch';
                    preloadLink.href = href;
                    document.head.appendChild(preloadLink);
                    preloadedPages.add(href);
                }
            });
        });

        // 2. Optimize images with lazy loading
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            if (!img.complete) {
                img.style.opacity = '0';
                img.onload = function() {
                    this.style.transition = 'opacity 0.3s ease';
                    this.style.opacity = '1';
                };
            }
        });

        // 3. Smooth scroll optimization
        document.documentElement.style.scrollBehavior = 'smooth';

        // 4. Optimize table rendering
        const tables = document.querySelectorAll('.table');
        tables.forEach(table => {
            // Add smooth scrolling to tables
            table.style.scrollBehavior = 'smooth';

            // Optimize large tables
            const rows = table.querySelectorAll('tbody tr');
            if (rows.length > 20) {
                // Add virtual scrolling for better performance
                table.style.maxHeight = '600px';
                table.style.overflowY = 'auto';
            }
        });

        // 5. Prefetch critical CSS and JS
        const criticalResources = [
            'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
        ];

        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'prefetch';
            link.href = resource;
            document.head.appendChild(link);
        });

        // 6. Optimize animations with requestAnimationFrame
        const animateElements = document.querySelectorAll('.card, .btn, .nav-link');
        animateElements.forEach(element => {
            element.style.willChange = 'transform, opacity';
        });

        // 7. ปรับปรุงการจัดการความสูงของหน้าเว็บ
        function optimizePageHeight() {
            // ลบ min-height ที่ไม่จำเป็น
            document.body.style.minHeight = 'auto';
            document.documentElement.style.minHeight = 'auto';

            // ตรวจสอบและปรับปรุงความสูงของ main wrapper
            const mainWrapper = document.querySelector('.main-wrapper');
            if (mainWrapper) {
                mainWrapper.style.minHeight = 'auto';
                mainWrapper.style.height = 'auto';
            }

            // ปรับปรุงการแสดงผลของ container
            const containers = document.querySelectorAll('.container, .container-fluid');
            containers.forEach(container => {
                container.style.height = 'auto';
                container.style.minHeight = 'auto';
            });
        }

        // เรียกใช้ฟังก์ชันเมื่อโหลดหน้าเสร็จ
        optimizePageHeight();

        // 8. Debounce scroll events for better performance
        let scrollTimeout;
        window.addEventListener('scroll', function() {
            if (scrollTimeout) {
                clearTimeout(scrollTimeout);
            }
            scrollTimeout = setTimeout(function() {
                // Optimize scroll-based animations
                const cards = document.querySelectorAll('.card');
                cards.forEach(card => {
                    const rect = card.getBoundingClientRect();
                    if (rect.top < window.innerHeight && rect.bottom > 0) {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }
                });
            }, 10);
        }, { passive: true });

        // 9. Optimize click events
        document.addEventListener('click', function(e) {
            // Add instant feedback for all clickable elements
            const clickable = e.target.closest('button, a, .btn, .nav-link');
            if (clickable) {
                clickable.style.transform = 'scale(0.98)';
                requestAnimationFrame(() => {
                    clickable.style.transform = '';
                });
            }
        }, { passive: true });

        // 10. ป้องกันการเลื่อนเกินจำเป็น
        function preventExcessiveScrolling() {
            const body = document.body;
            const html = document.documentElement;

            // คำนวณความสูงจริงของเนื้อหา
            const contentHeight = Math.max(
                body.scrollHeight,
                body.offsetHeight,
                html.clientHeight,
                html.scrollHeight,
                html.offsetHeight
            );

            // ตั้งค่าความสูงสูงสุดให้เหมาะสม
            const maxHeight = contentHeight + 100; // เพิ่ม padding 100px
            body.style.maxHeight = maxHeight + 'px';
            html.style.maxHeight = maxHeight + 'px';
        }

        // เรียกใช้หลังจากโหลดเนื้อหาเสร็จ
        setTimeout(preventExcessiveScrolling, 500);

        // 11. เพิ่มการตรวจสอบและปรับปรุงความสูงเมื่อมีการเปลี่ยนแปลง
        const observer = new MutationObserver(function(mutations) {
            let shouldUpdate = false;
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' || mutation.type === 'attributes') {
                    shouldUpdate = true;
                }
            });

            if (shouldUpdate) {
                setTimeout(() => {
                    optimizePageHeight();
                    preventExcessiveScrolling();
                }, 100);
            }
        });

        // เริ่มการสังเกตการณ์
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style', 'class']
        });

        // 9. Preload next/previous pages in pagination
        const paginationLinks = document.querySelectorAll('.pagination a');
        paginationLinks.forEach(link => {
            link.addEventListener('mouseenter', function() {
                if (!preloadedPages.has(this.href)) {
                    const preloadLink = document.createElement('link');
                    preloadLink.rel = 'prefetch';
                    preloadLink.href = this.href;
                    document.head.appendChild(preloadLink);
                    preloadedPages.add(this.href);
                }
            });
        });

        // 10. Optimize memory usage
        window.addEventListener('beforeunload', function() {
            // Clean up event listeners and timers
            if (scrollTimeout) clearTimeout(scrollTimeout);
        });

        // ซ่อน alert อัตโนมัติ
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                if (alert.classList.contains('show')) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            });
        }, 5000);

        // Ultra-smooth form handling
        document.querySelectorAll('form').forEach(form => {
            // Add smooth validation feedback
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.style.transform = 'scale(1.02)';
                    this.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                });

                input.addEventListener('blur', function() {
                    this.style.transform = 'scale(1)';
                    this.style.boxShadow = '';
                });
            });

            form.addEventListener('submit', function() {
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn && !submitBtn.disabled) {
                    // Ultra-subtle feedback
                    submitBtn.style.opacity = '0.8';
                    submitBtn.style.transform = 'scale(0.98)';
                    submitBtn.disabled = true;

                    // Quick reset for smooth experience
                    setTimeout(() => {
                        submitBtn.style.opacity = '1';
                        submitBtn.style.transform = 'scale(1)';
                        submitBtn.disabled = false;
                    }, 3000);
                }
            });
        });

        // Smooth table interactions
        document.querySelectorAll('.table tbody tr').forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(2px)';
                this.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
            });

            row.addEventListener('mouseleave', function() {
                this.style.transform = 'translateX(0)';
                this.style.boxShadow = '';
            });
        });

        // เพิ่ม smooth scroll
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // เพิ่ม ripple effect ให้ปุ่ม
        document.querySelectorAll('.btn').forEach(button => {
            button.addEventListener('click', function(e) {
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                ripple.classList.add('ripple');

                this.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    </script>

    @yield('scripts')
</body>
</html>
