@extends('layouts.admin')

@section('title', 'เพิ่มแพ็คเกจใหม่ - ระบบจัดการ')

@section('breadcrumb')
<li class="breadcrumb-item"><a href="{{ route('admin.packages') }}">จัดการแพ็คเกจ</a></li>
<li class="breadcrumb-item active">เพิ่มแพ็คเกจใหม่</li>
@endsection

@section('content')
<div class="content-safe-area">
<!-- Header Section - เรียบง่าย -->
<div class="row mb-4">
    <div class="col-12">
        <div class="bg-white rounded-3 p-4 border">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-1 text-primary">
                        <i class="fas fa-plus me-2"></i>เพิ่มแพ็คเกจใหม่
                    </h1>
                    <p class="text-muted mb-0">สร้างแพ็คเกจบริการใหม่พร้อมรายละเอียดและราคา</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('admin.packages') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>กลับไปแพ็คเกจ
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <form action="{{ route('admin.packages.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">ชื่อแพ็คเกจ <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                               id="name" name="name" value="{{ old('name') }}" required>
                        @error('name')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">คำอธิบาย <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="3" required>{{ old('description') }}</textarea>
                        @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="features" class="form-label">รายการที่รวมในแพ็คเกจ <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('features') is-invalid @enderror"
                                  id="features" name="features" rows="6" required
                                  placeholder="- รายการที่ 1&#10;- รายการที่ 2&#10;- รายการที่ 3">{{ old('features') }}</textarea>
                        @error('features')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">แต่ละรายการขึ้นบรรทัดใหม่</div>
                    </div>

                    <div class="mb-3">
                        <label for="price_text" class="form-label">ราคา</label>
                        <input type="text" class="form-control @error('price_text') is-invalid @enderror"
                               id="price_text" name="price_text" value="{{ old('price_text') }}"
                               placeholder="เช่น 50,000 บาท, ตามตกลง, เริ่มต้น 30,000 บาท, สอบถาม">
                        @error('price_text')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">สามารถใส่ตัวเลข ข้อความ หรือทั้งคู่ได้ หากไม่ใส่จะแสดงข้อความ "สอบถามราคา"</div>
                    </div>

                    <div class="mb-3">
                        <label for="duration" class="form-label">ระยะเวลา</label>
                        <input type="text" class="form-control @error('duration') is-invalid @enderror"
                               id="duration" name="duration" value="{{ old('duration') }}"
                               placeholder="เช่น ครั้งเดียว, 1 ปี, 6 เดือน">
                        @error('duration')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="sort_order" class="form-label">ลำดับการแสดง</label>
                        <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                               id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0">
                        @error('sort_order')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="image" class="form-label">รูปภาพ</label>
                        <input type="file" class="form-control @error('image') is-invalid @enderror" 
                               id="image" name="image" accept="image/*">
                        @error('image')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">รองรับไฟล์: JPG, PNG, GIF ขนาดไม่เกิน 2MB</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" 
                                           value="1" {{ old('is_featured') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_featured">
                                        แพ็คเกจแนะนำ
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        เปิดใช้งาน
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>บันทึก
                        </button>
                        <a href="{{ route('admin.packages') }}" class="btn btn-secondary">ยกเลิก</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    

</div>
</div> <!-- ปิด content-safe-area -->
@endsection
