<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Activity;
use App\Models\ActivityImage;
use App\Models\Service;
use App\Models\Package;

class PaginationTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create test activities for pagination (20 items = 3 pages with 9 per page)
        for ($i = 1; $i <= 20; $i++) {
            $activity = Activity::create([
                'title' => "ผลงานทดสอบ #{$i}",
                'description' => "คำอธิบายผลงานทดสอบหมายเลข {$i} สำหรับทดสอบระบบ pagination",
                'details' => "รายละเอียดเพิ่มเติมของผลงานทดสอบหมายเลข {$i}",
                'activity_date' => now()->subDays(rand(1, 365)),
                'location' => "สถานที่ทดสอบ {$i}",
                'image' => 'activities/default.jpg',
                'is_active' => rand(0, 1),
                'sort_order' => $i
            ]);

            // Create sample images for each activity
            for ($j = 1; $j <= rand(2, 5); $j++) {
                ActivityImage::create([
                    'activity_id' => $activity->id,
                    'image_path' => "activities/test-{$i}-{$j}.jpg",
                    'caption' => "รูปภาพทดสอบ {$j} ของผลงาน {$i}",
                    'is_cover' => $j === 1,
                    'sort_order' => $j
                ]);
            }
        }

        // Create test services for pagination (15 items = 2 pages with 9 per page)
        for ($i = 1; $i <= 15; $i++) {
            Service::create([
                'title' => "บริการทดสอบ #{$i}",
                'description' => "คำอธิบายบริการทดสอบหมายเลข {$i}",
                'details' => "รายละเอียดบริการทดสอบหมายเลข {$i}",
                'image' => 'services/test-service-' . $i . '.jpg',
                'is_active' => rand(0, 1),
                'sort_order' => $i
            ]);
        }

        // Create test packages for pagination (12 items = 2 pages with 9 per page)
        for ($i = 1; $i <= 12; $i++) {
            Package::create([
                'name' => "แพ็คเกจทดสอบ #{$i}",
                'description' => "คำอธิบายแพ็คเกจทดสอบหมายเลข {$i}",
                'features' => "รายละเอียดแพ็คเกจทดสอบหมายเลข {$i}",
                'duration' => rand(1, 12) . ' เดือน',
                'image' => 'packages/test-package-' . $i . '.jpg',
                'is_active' => rand(0, 1),
                'is_featured' => rand(0, 1),
                'sort_order' => $i
            ]);
        }

        $this->command->info('✅ สร้างข้อมูลทดสอบ Pagination เรียบร้อยแล้ว!');
        $this->command->info('📊 Activities: 20 รายการ (3 หน้า: 9+9+2)');
        $this->command->info('🔧 Services: 15 รายการ (2 หน้า: 9+6)');
        $this->command->info('📦 Packages: 12 รายการ (2 หน้า: 9+3)');
        $this->command->info('🎯 ทุกหน้าใช้ 9 รายการต่อหน้าเหมือนกัน');
    }
}
